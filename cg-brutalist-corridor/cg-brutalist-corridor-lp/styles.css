* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  background-color: #0f0f0f;
}

.corridor {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  font-family: "PP Neue Montreal";
  font-size: 13px;
  color: #fff;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1em;
}

.counter p {
  color: #fff;
}

h1 {
  position: absolute;
  bottom: 1em;
  left: 1em;
  width: 60%;
  text-transform: uppercase;
  font-family: "LomoCopy Lt Std";
  color: #fff;
  user-select: none;
}

p,
a {
  text-decoration: none;
  text-transform: uppercase;
  font-family: "Akkurat Mono";
  font-size: 12px;
  color: #fff;
}

.hero {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 2;
  mix-blend-mode: difference;
}

nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  padding: 2em;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-items {
  display: flex;
  gap: 2em;
}

.footer {
  position: absolute;
  right: 2em;
  bottom: 2em;
}
