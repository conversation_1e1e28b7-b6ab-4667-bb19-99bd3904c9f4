<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Brutalist Sci-fi Corridor Landing Page | Codegrid</title>
    <link rel="stylesheet" href="./styles.css" />
  </head>
  <body>
    <div class="corridor"></div>

    <div class="loading">Loading Scene</div>

    <div class="overlay">
      <div class="counter"><p>0</p></div>
    </div>

    <div class="hero">
      <nav>
        <div class="logo">
          <a href="#">Astrolume</a>
        </div>
        <div class="nav-items">
          <a href="#">Apparel</a>
          <a href="#">Events</a>
          <a href="#">Archive</a>
        </div>
        <div class="site-year">
          <p>2024 [N]</p>
        </div>
      </nav>

      <h1>
        Blending contemporary minimalism with futuristic innovation to create
        designs that transcend trends and define elegance.
      </h1>

      <div class="footer">
        <p>/ Made by Codegrid</p>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/EffectComposer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/RenderPass.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/ShaderPass.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/shaders/CopyShader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/shaders/LuminosityHighPassShader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/UnrealBloomPass.js"></script>
    <script src="./script.js"></script>
  </body>
</html>
