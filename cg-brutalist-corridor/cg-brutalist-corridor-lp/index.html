<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Combat Mirror Training Facility | 3D Virtual Gym Experience</title>
    <link rel="stylesheet" href="./styles.css" />
  </head>
  <body>
    <div class="corridor"></div>

    <div class="loading">Loading Training Facility</div>

    <div class="overlay">
      <div class="counter"><p>0</p></div>
    </div>

    <div class="hero">
      <nav>
        <div class="logo">
          <a href="#">Combat Mirror</a>
        </div>
        <div class="nav-items">
          <a href="#">Training</a>
          <a href="#">Equipment</a>
          <a href="#">Facilities</a>
        </div>
        <div class="site-year">
          <p>2024 [CM]</p>
        </div>
      </nav>

      <h1>
        Engineering precision training environments with cutting-edge technology to create
        facilities that elevate performance and define championship excellence.
      </h1>

      <div class="footer">
        <p>/ Built with Precision</p>
      </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/EffectComposer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/RenderPass.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/ShaderPass.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/shaders/CopyShader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/shaders/LuminosityHighPassShader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/UnrealBloomPass.js"></script>
    <script src="./script.js"></script>
  </body>
</html>
