{"version": 3, "file": "CustomEase.min.js", "sources": ["../src/utils/paths.js", "../src/CustomEase.js"], "sourcesContent": ["/*!\n * paths 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_numbersExp = /(?:(-)?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n\t_scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n\t_selectorExp = /(^[#\\.][a-z]|[a-y][a-z])/i,\n\t_DEG2RAD = Math.PI / 180,\n\t_RAD2DEG = 180 / Math.PI,\n\t_sin = Math.sin,\n\t_cos = Math.cos,\n\t_abs = Math.abs,\n\t_sqrt = Math.sqrt,\n\t_atan2 = Math.atan2,\n\t_largeNum = 1e8,\n\t_isString = value => typeof(value) === \"string\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isUndefined = value => typeof(value) === \"undefined\",\n\t_temp = {},\n\t_temp2 = {},\n\t_roundingNum = 1e5,\n\t_wrapProgress = progress => (Math.round((progress + _largeNum) % 1 * _roundingNum) / _roundingNum) || ((progress < 0) ? 0 : 1), //if progress lands on 1, the % will make it 0 which is why we || 1, but not if it's negative because it makes more sense for motion to end at 0 in that case.\n\t_round = value => (Math.round(value * _roundingNum) / _roundingNum) || 0,\n\t_roundPrecise = value => (Math.round(value * 1e10) / 1e10) || 0,\n\t_splitSegment = (rawPath, segIndex, i, t) => {\n\t\tlet segment = rawPath[segIndex],\n\t\t\tshift = t === 1 ? 6 : subdivideSegment(segment, i, t);\n\t\tif ((shift || !t) && shift + i + 2 < segment.length) {\n\t\t\trawPath.splice(segIndex, 0, segment.slice(0, i + shift + 2));\n\t\t\tsegment.splice(0, i + shift);\n\t\t\treturn 1;\n\t\t}\n\t},\n\t_getSampleIndex = (samples, length, progress) => {\n\t\t// slightly slower way than doing this (when there's no lookup): segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0;\n\t\tlet l = samples.length,\n\t\t\ti = ~~(progress * l);\n\t\tif (samples[i] > length) {\n\t\t\twhile (--i && samples[i] > length) {}\n\t\t\ti < 0 && (i = 0);\n\t\t} else {\n\t\t\twhile (samples[++i] < length && i < l) {}\n\t\t}\n\t\treturn i < l ? i : l - 1;\n\t},\n\t_reverseRawPath = (rawPath, skipOuter) => {\n\t\tlet i = rawPath.length;\n\t\tskipOuter || rawPath.reverse();\n\t\twhile (i--) {\n\t\t\trawPath[i].reversed || reverseSegment(rawPath[i]);\n\t\t}\n\t},\n\t_copyMetaData = (source, copy) => {\n\t\tcopy.totalLength = source.totalLength;\n\t\tif (source.samples) { //segment\n\t\t\tcopy.samples = source.samples.slice(0);\n\t\t\tcopy.lookup = source.lookup.slice(0);\n\t\t\tcopy.minLength = source.minLength;\n\t\t\tcopy.resolution = source.resolution;\n\t\t} else if (source.totalPoints) { //rawPath\n\t\t\tcopy.totalPoints = source.totalPoints;\n\t\t}\n\t\treturn copy;\n\t},\n\t//pushes a new segment into a rawPath, but if its starting values match the ending values of the last segment, it'll merge it into that same segment (to reduce the number of segments)\n\t_appendOrMerge = (rawPath, segment) => {\n\t\tlet index = rawPath.length,\n\t\t\tprevSeg = rawPath[index - 1] || [],\n\t\t\tl = prevSeg.length;\n\t\tif (index && segment[0] === prevSeg[l-2] && segment[1] === prevSeg[l-1]) {\n\t\t\tsegment = prevSeg.concat(segment.slice(2));\n\t\t\tindex--;\n\t\t}\n\t\trawPath[index] = segment;\n\t},\n\t_bestDistance;\n\n/* TERMINOLOGY\n - RawPath - an array of arrays, one for each Segment. A single RawPath could have multiple \"M\" commands, defining Segments (paths aren't always connected).\n - Segment - an array containing a sequence of Cubic Bezier coordinates in alternating x, y, x, y format. Starting anchor, then control point 1, control point 2, and ending anchor, then the next control point 1, control point 2, anchor, etc. Uses less memory than an array with a bunch of {x, y} points.\n - Bezier - a single cubic Bezier with a starting anchor, two control points, and an ending anchor.\n - the variable \"t\" is typically the position along an individual Bezier path (time) and it's NOT linear, meaning it could accelerate/decelerate based on the control points whereas the \"p\" or \"progress\" value is linearly mapped to the whole path, so it shouldn't really accelerate/decelerate based on control points. So a progress of 0.2 would be almost exactly 20% along the path. \"t\" is ONLY in an individual Bezier piece.\n */\n\n//accepts basic selector text, a path instance, a RawPath instance, or a Segment and returns a RawPath (makes it easy to homogenize things). If an element or selector text is passed in, it'll also cache the value so that if it's queried again, it'll just take the path data from there instead of parsing it all over again (as long as the path data itself hasn't changed - it'll check).\nexport function getRawPath(value) {\n\tvalue = (_isString(value) && _selectorExp.test(value)) ? document.querySelector(value) || value : value;\n\tlet e = value.getAttribute ? value : 0,\n\t\trawPath;\n\tif (e && (value = value.getAttribute(\"d\"))) {\n\t\t//implements caching\n\t\tif (!e._gsPath) {\n\t\t\te._gsPath = {};\n\t\t}\n\t\trawPath = e._gsPath[value];\n\t\treturn (rawPath && !rawPath._dirty) ? rawPath : (e._gsPath[value] = stringToRawPath(value));\n\t}\n\treturn !value ? console.warn(\"Expecting a <path> element or an SVG path data string\") : _isString(value) ? stringToRawPath(value) : (_isNumber(value[0])) ? [value] : value;\n}\n\n//copies a RawPath WITHOUT the length meta data (for speed)\nexport function copyRawPath(rawPath) {\n\tlet a = [],\n\t\ti = 0;\n\tfor (; i < rawPath.length; i++) {\n\t\ta[i] = _copyMetaData(rawPath[i], rawPath[i].slice(0));\n\t}\n\treturn _copyMetaData(rawPath, a);\n}\n\nexport function reverseSegment(segment) {\n\tlet i = 0,\n\t\ty;\n\tsegment.reverse(); //this will invert the order y, x, y, x so we must flip it back.\n\tfor (; i < segment.length; i += 2) {\n\t\ty = segment[i];\n\t\tsegment[i] = segment[i+1];\n\t\tsegment[i+1] = y;\n\t}\n\tsegment.reversed = !segment.reversed;\n}\n\n\n\nlet _createPath = (e, ignore) => {\n\t\tlet path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\"),\n\t\t\tattr = [].slice.call(e.attributes),\n\t\t\ti = attr.length,\n\t\t\tname;\n\t\tignore = \",\" + ignore + \",\";\n\t\twhile (--i > -1) {\n\t\t\tname = attr[i].nodeName.toLowerCase(); //in Microsoft Edge, if you don't set the attribute with a lowercase name, it doesn't render correctly! Super weird.\n\t\t\tif (ignore.indexOf(\",\" + name + \",\") < 0) {\n\t\t\t\tpath.setAttributeNS(null, name, attr[i].nodeValue);\n\t\t\t}\n\t\t}\n\t\treturn path;\n\t},\n\t_typeAttrs = {\n\t\trect:\"rx,ry,x,y,width,height\",\n\t\tcircle:\"r,cx,cy\",\n\t\tellipse:\"rx,ry,cx,cy\",\n\t\tline:\"x1,x2,y1,y2\"\n\t},\n\t_attrToObj = (e, attrs) => {\n\t\tlet props = attrs ? attrs.split(\",\") : [],\n\t\t\tobj = {},\n\t\t\ti = props.length;\n\t\twhile (--i > -1) {\n\t\t\tobj[props[i]] = +e.getAttribute(props[i]) || 0;\n\t\t}\n\t\treturn obj;\n\t};\n\n//converts an SVG shape like <circle>, <rect>, <polygon>, <polyline>, <ellipse>, etc. to a <path>, swapping it in and copying the attributes to match.\nexport function convertToPath(element, swap) {\n\tlet type = element.tagName.toLowerCase(),\n\t\tcirc = 0.552284749831,\n\t\tdata, x, y, r, ry, path, rcirc, rycirc, points, w, h, x2, x3, x4, x5, x6, y2, y3, y4, y5, y6, attr;\n\tif (type === \"path\" || !element.getBBox) {\n\t\treturn element;\n\t}\n\tpath = _createPath(element, \"x,y,width,height,cx,cy,rx,ry,r,x1,x2,y1,y2,points\");\n\tattr = _attrToObj(element, _typeAttrs[type]);\n\tif (type === \"rect\") {\n\t\tr = attr.rx;\n\t\try = attr.ry || r;\n\t\tx = attr.x;\n\t\ty = attr.y;\n\t\tw = attr.width - r * 2;\n\t\th = attr.height - ry * 2;\n\t\tif (r || ry) { //if there are rounded corners, render cubic beziers\n\t\t\tx2 = x + r * (1 - circ);\n\t\t\tx3 = x + r;\n\t\t\tx4 = x3 + w;\n\t\t\tx5 = x4 + r * circ;\n\t\t\tx6 = x4 + r;\n\t\t\ty2 = y + ry * (1 - circ);\n\t\t\ty3 = y + ry;\n\t\t\ty4 = y3 + h;\n\t\t\ty5 = y4 + ry * circ;\n\t\t\ty6 = y4 + ry;\n\t\t\tdata = \"M\" + x6 + \",\" + y3 + \" V\" + y4 + \" C\" + [x6, y5, x5, y6, x4, y6, x4 - (x4 - x3) / 3, y6, x3 + (x4 - x3) / 3, y6, x3, y6, x2, y6, x, y5, x, y4, x, y4 - (y4 - y3) / 3, x, y3 + (y4 - y3) / 3, x, y3, x, y2, x2, y, x3, y, x3 + (x4 - x3) / 3, y, x4 - (x4 - x3) / 3, y, x4, y, x5, y, x6, y2, x6, y3].join(\",\") + \"z\";\n\t\t} else {\n\t\t\tdata = \"M\" + (x + w) + \",\" + y + \" v\" + h + \" h\" + (-w) + \" v\" + (-h) + \" h\" + w + \"z\";\n\t\t}\n\n\t} else if (type === \"circle\" || type === \"ellipse\") {\n\t\tif (type === \"circle\") {\n\t\t\tr = ry = attr.r;\n\t\t\trycirc = r * circ;\n\t\t} else {\n\t\t\tr = attr.rx;\n\t\t\try = attr.ry;\n\t\t\trycirc = ry * circ;\n\t\t}\n\t\tx = attr.cx;\n\t\ty = attr.cy;\n\t\trcirc = r * circ;\n\t\tdata = \"M\" + (x+r) + \",\" + y + \" C\" + [x+r, y + rycirc, x + rcirc, y + ry, x, y + ry, x - rcirc, y + ry, x - r, y + rycirc, x - r, y, x - r, y - rycirc, x - rcirc, y - ry, x, y - ry, x + rcirc, y - ry, x + r, y - rycirc, x + r, y].join(\",\") + \"z\";\n\t} else if (type === \"line\") {\n\t\tdata = \"M\" + attr.x1 + \",\" + attr.y1 + \" L\" + attr.x2 + \",\" + attr.y2; //previously, we just converted to \"Mx,y Lx,y\" but Safari has bugs that cause that not to render properly when using a stroke-dasharray that's not fully visible! Using a cubic bezier fixes that issue.\n\t} else if (type === \"polyline\" || type === \"polygon\") {\n\t\tpoints = (element.getAttribute(\"points\") + \"\").match(_numbersExp) || [];\n\t\tx = points.shift();\n\t\ty = points.shift();\n\t\tdata = \"M\" + x + \",\" + y + \" L\" + points.join(\",\");\n\t\tif (type === \"polygon\") {\n\t\t\tdata += \",\" + x + \",\" + y + \"z\";\n\t\t}\n\t}\n\tpath.setAttribute(\"d\", rawPathToString(path._gsRawPath = stringToRawPath(data)));\n\tif (swap && element.parentNode) {\n\t\telement.parentNode.insertBefore(path, element);\n\t\telement.parentNode.removeChild(element);\n\t}\n\treturn path;\n}\n\n\n\n//returns the rotation (in degrees) at a particular progress on a rawPath (the slope of the tangent)\nexport function getRotationAtProgress(rawPath, progress) {\n\tlet d = getProgressData(rawPath, progress >= 1 ? 1 - 1e-9 : progress ? progress : 1e-9);\n\treturn getRotationAtBezierT(d.segment, d.i, d.t);\n}\n\nfunction getRotationAtBezierT(segment, i, t) {\n\tlet a = segment[i],\n\t\tb = segment[i+2],\n\t\tc = segment[i+4],\n\t\tx;\n\ta += (b - a) * t;\n\tb += (c - b) * t;\n\ta += (b - a) * t;\n\tx = b + ((c + (segment[i+6] - c) * t) - b) * t - a;\n\ta = segment[i+1];\n\tb = segment[i+3];\n\tc = segment[i+5];\n\ta += (b - a) * t;\n\tb += (c - b) * t;\n\ta += (b - a) * t;\n\treturn _round(_atan2(b + ((c + (segment[i+7] - c) * t) - b) * t - a, x) * _RAD2DEG);\n}\n\nexport function sliceRawPath(rawPath, start, end) {\n\tend = _isUndefined(end) ? 1 : _roundPrecise(end) || 0; // we must round to avoid issues like 4.15 / 8 = 0.8300000000000001 instead of 0.83 or 2.8 / 5 = 0.5599999999999999 instead of 0.56 and if someone is doing a loop like start: 2.8 / 0.5, end: 2.8 / 0.5 + 1.\n\tstart = _roundPrecise(start) || 0;\n\tlet loops = Math.max(0, ~~(_abs(end - start) - 1e-8)),\n\t\tpath = copyRawPath(rawPath);\n\tif (start > end) {\n\t\tstart = 1 - start;\n\t\tend = 1 - end;\n\t\t_reverseRawPath(path);\n\t\tpath.totalLength = 0;\n\t}\n\tif (start < 0 || end < 0) {\n\t\tlet offset = Math.abs(~~Math.min(start, end)) + 1;\n\t\tstart += offset;\n\t\tend += offset;\n\t}\n\tpath.totalLength || cacheRawPathMeasurements(path);\n\tlet wrap = (end > 1),\n\t\ts = getProgressData(path, start, _temp, true),\n\t\te = getProgressData(path, end, _temp2),\n\t\teSeg = e.segment,\n\t\tsSeg = s.segment,\n\t\teSegIndex = e.segIndex,\n\t\tsSegIndex = s.segIndex,\n\t\tei = e.i,\n\t\tsi = s.i,\n\t\tsameSegment = (sSegIndex === eSegIndex),\n\t\tsameBezier = (ei === si && sameSegment),\n\t\twrapsBehind, sShift, eShift, i, copy, totalSegments, l, j;\n\tif (wrap || loops) {\n\t\twrapsBehind = eSegIndex < sSegIndex || (sameSegment && ei < si) || (sameBezier && e.t < s.t);\n\t\tif (_splitSegment(path, sSegIndex, si, s.t)) {\n\t\t\tsSegIndex++;\n\t\t\tif (!wrapsBehind) {\n\t\t\t\teSegIndex++;\n\t\t\t\tif (sameBezier) {\n\t\t\t\t\te.t = (e.t - s.t) / (1 - s.t);\n\t\t\t\t\tei = 0;\n\t\t\t\t} else if (sameSegment) {\n\t\t\t\t\tei -= si;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (Math.abs(1 - (end - start)) < 1e-5) {\n\t\t\teSegIndex = sSegIndex - 1;\n\t\t} else if (!e.t && eSegIndex) {\n\t\t\teSegIndex--;\n\t\t} else if (_splitSegment(path, eSegIndex, ei, e.t) && wrapsBehind) {\n\t\t\tsSegIndex++;\n\t\t}\n\t\tif (s.t === 1) {\n\t\t\tsSegIndex = (sSegIndex + 1) % path.length;\n\t\t}\n\t\tcopy = [];\n\t\ttotalSegments = path.length;\n\t\tl = 1 + totalSegments * loops;\n\t\tj = sSegIndex;\n\t\tl += ((totalSegments - sSegIndex) + eSegIndex) % totalSegments;\n\t\tfor (i = 0; i < l; i++) {\n\t\t\t_appendOrMerge(copy, path[j++ % totalSegments]);\n\t\t}\n\t\tpath = copy;\n\t} else {\n\t\teShift = e.t === 1 ? 6 : subdivideSegment(eSeg, ei, e.t);\n\t\tif (start !== end) {\n\t\t\tsShift = subdivideSegment(sSeg, si, sameBezier ? s.t / e.t : s.t);\n\t\t\tsameSegment && (eShift += sShift);\n\t\t\teSeg.splice(ei + eShift + 2);\n\t\t\t(sShift || si) && sSeg.splice(0, si + sShift);\n\t\t\ti = path.length;\n\t\t\twhile (i--) {\n\t\t\t\t//chop off any extra segments\n\t\t\t\t(i < sSegIndex || i > eSegIndex) &&\tpath.splice(i, 1);\n\t\t\t}\n\t\t} else {\n\t\t\teSeg.angle = getRotationAtBezierT(eSeg, ei + eShift, 0); //record the value before we chop because it'll be impossible to determine the angle after its length is 0!\n\t\t\tei += eShift;\n\t\t\ts = eSeg[ei];\n\t\t\te = eSeg[ei+1];\n\t\t\teSeg.length = eSeg.totalLength = 0;\n\t\t\teSeg.totalPoints = path.totalPoints = 8;\n\t\t\teSeg.push(s, e, s, e, s, e, s, e);\n\t\t}\n\t}\n\tpath.totalLength = 0;\n\treturn path;\n}\n\n//measures a Segment according to its resolution (so if segment.resolution is 6, for example, it'll take 6 samples equally across each Bezier) and create/populate a \"samples\" Array that has the length up to each of those sample points (always increasing from the start) as well as a \"lookup\" array that's broken up according to the smallest distance between 2 samples. This gives us a very fast way of looking up a progress position rather than looping through all the points/Beziers. You can optionally have it only measure a subset, starting at startIndex and going for a specific number of beziers (remember, there are 3 x/y pairs each, for a total of 6 elements for each Bezier). It will also populate a \"totalLength\" property, but that's not generally super accurate because by default it'll only take 6 samples per Bezier. But for performance reasons, it's perfectly adequate for measuring progress values along the path. If you need a more accurate totalLength, either increase the resolution or use the more advanced bezierToPoints() method which keeps adding points until they don't deviate by more than a certain precision value.\nfunction measureSegment(segment, startIndex, bezierQty) {\n\tstartIndex = startIndex || 0;\n\tif (!segment.samples) {\n\t\tsegment.samples = [];\n\t\tsegment.lookup = [];\n\t}\n\tlet resolution = ~~segment.resolution || 12,\n\t\tinc = 1 / resolution,\n\t\tendIndex = bezierQty ? startIndex + bezierQty * 6 + 1 : segment.length,\n\t\tx1 = segment[startIndex],\n\t\ty1 = segment[startIndex + 1],\n\t\tsamplesIndex = startIndex ? (startIndex / 6) * resolution : 0,\n\t\tsamples = segment.samples,\n\t\tlookup = segment.lookup,\n\t\tmin = (startIndex ? segment.minLength : _largeNum) || _largeNum,\n\t\tprevLength = samples[samplesIndex + bezierQty * resolution - 1],\n\t\tlength = startIndex ? samples[samplesIndex-1] : 0,\n\t\ti, j, x4, x3, x2, xd, xd1, y4, y3, y2, yd, yd1, inv, t, lengthIndex, l, segLength;\n\tsamples.length = lookup.length = 0;\n\tfor (j = startIndex + 2; j < endIndex; j += 6) {\n\t\tx4 = segment[j + 4] - x1;\n\t\tx3 = segment[j + 2] - x1;\n\t\tx2 = segment[j] - x1;\n\t\ty4 = segment[j + 5] - y1;\n\t\ty3 = segment[j + 3] - y1;\n\t\ty2 = segment[j + 1] - y1;\n\t\txd = xd1 = yd = yd1 = 0;\n\t\tif (_abs(x4) < .01 && _abs(y4) < .01 && _abs(x2) + _abs(y2) < .01) { //dump points that are sufficiently close (basically right on top of each other, making a bezier super tiny or 0 length)\n\t\t\tif (segment.length > 8) {\n\t\t\t\tsegment.splice(j, 6);\n\t\t\t\tj -= 6;\n\t\t\t\tendIndex -= 6;\n\t\t\t}\n\t\t} else {\n\t\t\tfor (i = 1; i <= resolution; i++) {\n\t\t\t\tt = inc * i;\n\t\t\t\tinv = 1 - t;\n\t\t\t\txd = xd1 - (xd1 = (t * t * x4 + 3 * inv * (t * x3 + inv * x2)) * t);\n\t\t\t\tyd = yd1 - (yd1 = (t * t * y4 + 3 * inv * (t * y3 + inv * y2)) * t);\n\t\t\t\tl = _sqrt(yd * yd + xd * xd);\n\t\t\t\tif (l < min) {\n\t\t\t\t\tmin = l;\n\t\t\t\t}\n\t\t\t\tlength += l;\n\t\t\t\tsamples[samplesIndex++] = length;\n\t\t\t}\n\t\t}\n\t\tx1 += x4;\n\t\ty1 += y4;\n\t}\n\tif (prevLength) {\n\t\tprevLength -= length;\n\t\tfor (; samplesIndex < samples.length; samplesIndex++) {\n\t\t\tsamples[samplesIndex] += prevLength;\n\t\t}\n\t}\n\tif (samples.length && min) {\n\t\tsegment.totalLength = segLength = samples[samples.length-1] || 0;\n\t\tsegment.minLength = min;\n\t\tif (segLength / min < 9999) { // if the lookup would require too many values (memory problem), we skip this and instead we use a loop to lookup values directly in the samples Array\n\t\t\tl = lengthIndex = 0;\n\t\t\tfor (i = 0; i < segLength; i += min) {\n\t\t\t\tlookup[l++] = (samples[lengthIndex] < i) ? ++lengthIndex : lengthIndex;\n\t\t\t}\n\t\t}\n\t} else {\n\t\tsegment.totalLength = samples[0] = 0;\n\t}\n\treturn startIndex ? length - samples[startIndex / 2 - 1] : length;\n}\n\nexport function cacheRawPathMeasurements(rawPath, resolution) {\n\tlet pathLength, points, i;\n\tfor (i = pathLength = points = 0; i < rawPath.length; i++) {\n\t\trawPath[i].resolution = ~~resolution || 12; //steps per Bezier curve (anchor, 2 control points, to anchor)\n\t\tpoints += rawPath[i].length;\n\t\tpathLength += measureSegment(rawPath[i]);\n\t}\n\trawPath.totalPoints = points;\n\trawPath.totalLength = pathLength;\n\treturn rawPath;\n}\n\n//divide segment[i] at position t (value between 0 and 1, progress along that particular cubic bezier segment that starts at segment[i]). Returns how many elements were spliced into the segment array (either 0 or 6)\nexport function subdivideSegment(segment, i, t) {\n\tif (t <= 0 || t >= 1) {\n\t\treturn 0;\n\t}\n\tlet ax = segment[i],\n\t\tay = segment[i+1],\n\t\tcp1x = segment[i+2],\n\t\tcp1y = segment[i+3],\n\t\tcp2x = segment[i+4],\n\t\tcp2y = segment[i+5],\n\t\tbx = segment[i+6],\n\t\tby = segment[i+7],\n\t\tx1a = ax + (cp1x - ax) * t,\n\t\tx2 = cp1x + (cp2x - cp1x) * t,\n\t\ty1a = ay + (cp1y - ay) * t,\n\t\ty2 = cp1y + (cp2y - cp1y) * t,\n\t\tx1 = x1a + (x2 - x1a) * t,\n\t\ty1 = y1a + (y2 - y1a) * t,\n\t\tx2a = cp2x + (bx - cp2x) * t,\n\t\ty2a = cp2y + (by - cp2y) * t;\n\tx2 += (x2a - x2) * t;\n\ty2 += (y2a - y2) * t;\n\tsegment.splice(i + 2, 4,\n\t\t_round(x1a),                  //first control point\n\t\t_round(y1a),\n\t\t_round(x1),                   //second control point\n\t\t_round(y1),\n\t\t_round(x1 + (x2 - x1) * t),   //new fabricated anchor on line\n\t\t_round(y1 + (y2 - y1) * t),\n\t\t_round(x2),                   //third control point\n\t\t_round(y2),\n\t\t_round(x2a),                  //fourth control point\n\t\t_round(y2a)\n\t);\n\tsegment.samples && segment.samples.splice(((i / 6) * segment.resolution) | 0, 0, 0, 0, 0, 0, 0, 0);\n\treturn 6;\n}\n\n// returns an object {path, segment, segIndex, i, t}\nfunction getProgressData(rawPath, progress, decoratee, pushToNextIfAtEnd) {\n\tdecoratee = decoratee || {};\n\trawPath.totalLength || cacheRawPathMeasurements(rawPath);\n\tif (progress < 0 || progress > 1) {\n\t\tprogress = _wrapProgress(progress);\n\t}\n\tlet segIndex = 0,\n\t\tsegment = rawPath[0],\n\t\tsamples, resolution, length, min, max, i, t;\n\tif (!progress) {\n\t\tt = i = segIndex = 0;\n\t\tsegment = rawPath[0];\n\t} else if (progress === 1) {\n\t\tt = 1;\n\t\tsegIndex = rawPath.length - 1;\n\t\tsegment = rawPath[segIndex];\n\t\ti = segment.length - 8;\n\t} else {\n\t\tif (rawPath.length > 1) { //speed optimization: most of the time, there's only one segment so skip the recursion.\n\t\t\tlength = rawPath.totalLength * progress;\n\t\t\tmax = i = 0;\n\t\t\twhile ((max += rawPath[i++].totalLength) < length) {\n\t\t\t\tsegIndex = i;\n\t\t\t}\n\t\t\tsegment = rawPath[segIndex];\n\t\t\tmin = max - segment.totalLength;\n\t\t\tprogress = ((length - min) / (max - min)) || 0;\n\t\t}\n\t\tsamples = segment.samples;\n\t\tresolution = segment.resolution; //how many samples per cubic bezier chunk\n\t\tlength = segment.totalLength * progress;\n\t\ti = segment.lookup.length ? segment.lookup[~~(length / segment.minLength)] || 0 : _getSampleIndex(samples, length, progress);\n\t\tmin = i ? samples[i-1] : 0;\n\t\tmax = samples[i];\n\t\tif (max < length) {\n\t\t\tmin = max;\n\t\t\tmax = samples[++i];\n\t\t}\n\t\tt = (1 / resolution) * (((length - min) / (max - min)) + ((i % resolution)));\n\t\ti = ~~(i / resolution) * 6;\n\t\tif (pushToNextIfAtEnd && t === 1) {\n\t\t\tif (i + 6 < segment.length) {\n\t\t\t\ti += 6;\n\t\t\t\tt = 0;\n\t\t\t} else if (segIndex + 1 < rawPath.length) {\n\t\t\t\ti = t = 0;\n\t\t\t\tsegment = rawPath[++segIndex];\n\t\t\t}\n\t\t}\n\t}\n\tdecoratee.t = t;\n\tdecoratee.i = i;\n\tdecoratee.path = rawPath;\n\tdecoratee.segment = segment;\n\tdecoratee.segIndex = segIndex;\n\treturn decoratee;\n}\n\nexport function getPositionOnPath(rawPath, progress, includeAngle, point) {\n\tlet segment = rawPath[0],\n\t\tresult = point || {},\n\t\tsamples, resolution, length, min, max, i, t, a, inv;\n\tif (progress < 0 || progress > 1) {\n\t\tprogress = _wrapProgress(progress);\n\t}\n\tsegment.lookup || cacheRawPathMeasurements(rawPath);\n\tif (rawPath.length > 1) { //speed optimization: most of the time, there's only one segment so skip the recursion.\n\t\tlength = rawPath.totalLength * progress;\n\t\tmax = i = 0;\n\t\twhile ((max += rawPath[i++].totalLength) < length) {\n\t\t\tsegment = rawPath[i];\n\t\t}\n\t\tmin = max - segment.totalLength;\n\t\tprogress = ((length - min) / (max - min)) || 0;\n\t}\n\tsamples = segment.samples;\n\tresolution = segment.resolution;\n\tlength = segment.totalLength * progress;\n\ti = segment.lookup.length ? segment.lookup[progress < 1 ? ~~(length / segment.minLength) : segment.lookup.length - 1] || 0 : _getSampleIndex(samples, length, progress);\n\tmin = i ? samples[i-1] : 0;\n\tmax = samples[i];\n\tif (max < length) {\n\t\tmin = max;\n\t\tmax = samples[++i];\n\t}\n\tt = ((1 / resolution) * (((length - min) / (max - min)) + ((i % resolution)))) || 0;\n\tinv = 1 - t;\n\ti = ~~(i / resolution) * 6;\n\ta = segment[i];\n\tresult.x = _round((t * t * (segment[i + 6] - a) + 3 * inv * (t * (segment[i + 4] - a) + inv * (segment[i + 2] - a))) * t + a);\n\tresult.y = _round((t * t * (segment[i + 7] - (a = segment[i+1])) + 3 * inv * (t * (segment[i + 5] - a) + inv * (segment[i + 3] - a))) * t + a);\n\tif (includeAngle) {\n\t\tresult.angle = segment.totalLength ? getRotationAtBezierT(segment, i, t >= 1 ? 1 - 1e-9 : t ? t : 1e-9) : segment.angle || 0;\n\t}\n\treturn result;\n}\n\n\n\n//applies a matrix transform to RawPath (or a segment in a RawPath) and returns whatever was passed in (it transforms the values in the array(s), not a copy).\nexport function transformRawPath(rawPath, a, b, c, d, tx, ty) {\n\tlet j = rawPath.length,\n\t\tsegment, l, i, x, y;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tl = segment.length;\n\t\tfor (i = 0; i < l; i += 2) {\n\t\t\tx = segment[i];\n\t\t\ty = segment[i+1];\n\t\t\tsegment[i] = x * a + y * c + tx;\n\t\t\tsegment[i+1] = x * b + y * d + ty;\n\t\t}\n\t}\n\trawPath._dirty = 1;\n\treturn rawPath;\n}\n\n\n\n// translates SVG arc data into a segment (cubic beziers). Angle is in degrees.\nfunction arcToSegment(lastX, lastY, rx, ry, angle, largeArcFlag, sweepFlag, x, y) {\n\tif (lastX === x && lastY === y) {\n\t\treturn;\n\t}\n\trx = _abs(rx);\n\try = _abs(ry);\n\tlet angleRad = (angle % 360) * _DEG2RAD,\n\t\tcosAngle = _cos(angleRad),\n\t\tsinAngle = _sin(angleRad),\n\t\tPI = Math.PI,\n\t\tTWOPI = PI * 2,\n\t\tdx2 = (lastX - x) / 2,\n\t\tdy2 = (lastY - y) / 2,\n\t\tx1 = (cosAngle * dx2 + sinAngle * dy2),\n\t\ty1 = (-sinAngle * dx2 + cosAngle * dy2),\n\t\tx1_sq = x1 * x1,\n\t\ty1_sq = y1 * y1,\n\t\tradiiCheck = x1_sq / (rx * rx) + y1_sq / (ry * ry);\n\tif (radiiCheck > 1) {\n\t\trx = _sqrt(radiiCheck) * rx;\n\t\try = _sqrt(radiiCheck) * ry;\n\t}\n\tlet rx_sq = rx * rx,\n\t\try_sq = ry * ry,\n\t\tsq = ((rx_sq * ry_sq) - (rx_sq * y1_sq) - (ry_sq * x1_sq)) / ((rx_sq * y1_sq) + (ry_sq * x1_sq));\n\tif (sq < 0) {\n\t\tsq = 0;\n\t}\n\tlet coef = ((largeArcFlag === sweepFlag) ? -1 : 1) * _sqrt(sq),\n\t\tcx1 = coef * ((rx * y1) / ry),\n\t\tcy1 = coef * -((ry * x1) / rx),\n\t\tsx2 = (lastX + x) / 2,\n\t\tsy2 = (lastY + y) / 2,\n\t\tcx = sx2 + (cosAngle * cx1 - sinAngle * cy1),\n\t\tcy = sy2 + (sinAngle * cx1 + cosAngle * cy1),\n\t\tux = (x1 - cx1) / rx,\n\t\tuy = (y1 - cy1) / ry,\n\t\tvx = (-x1 - cx1) / rx,\n\t\tvy = (-y1 - cy1) / ry,\n\t\ttemp = ux * ux + uy * uy,\n\t\tangleStart = ((uy < 0) ? -1 : 1) * Math.acos(ux / _sqrt(temp)),\n\t\tangleExtent = ((ux * vy - uy * vx < 0) ? -1 : 1) * Math.acos((ux * vx + uy * vy) / _sqrt(temp * (vx * vx + vy * vy)));\n\tisNaN(angleExtent) && (angleExtent = PI); //rare edge case. Math.cos(-1) is NaN.\n\tif (!sweepFlag && angleExtent > 0) {\n\t\tangleExtent -= TWOPI;\n\t} else if (sweepFlag && angleExtent < 0) {\n\t\tangleExtent += TWOPI;\n\t}\n\tangleStart %= TWOPI;\n\tangleExtent %= TWOPI;\n\tlet segments = Math.ceil(_abs(angleExtent) / (TWOPI / 4)),\n\t\trawPath = [],\n\t\tangleIncrement = angleExtent / segments,\n\t\tcontrolLength = 4 / 3 * _sin(angleIncrement / 2) / (1 + _cos(angleIncrement / 2)),\n\t\tma = cosAngle * rx,\n\t\tmb = sinAngle * rx,\n\t\tmc = sinAngle * -ry,\n\t\tmd = cosAngle * ry,\n\t\ti;\n\tfor (i = 0; i < segments; i++) {\n\t\tangle = angleStart + i * angleIncrement;\n\t\tx1 = _cos(angle);\n\t\ty1 = _sin(angle);\n\t\tux = _cos(angle += angleIncrement);\n\t\tuy = _sin(angle);\n\t\trawPath.push(x1 - controlLength * y1, y1 + controlLength * x1, ux + controlLength * uy, uy - controlLength * ux, ux, uy);\n\t}\n\t//now transform according to the actual size of the ellipse/arc (the beziers were noramlized, between 0 and 1 on a circle).\n\tfor (i = 0; i < rawPath.length; i+=2) {\n\t\tx1 = rawPath[i];\n\t\ty1 = rawPath[i+1];\n\t\trawPath[i] = x1 * ma + y1 * mc + cx;\n\t\trawPath[i+1] = x1 * mb + y1 * md + cy;\n\t}\n\trawPath[i-2] = x; //always set the end to exactly where it's supposed to be\n\trawPath[i-1] = y;\n\treturn rawPath;\n}\n\n//Spits back a RawPath with absolute coordinates. Each segment starts with a \"moveTo\" command (x coordinate, then y) and then 2 control points (x, y, x, y), then anchor. The goal is to minimize memory and maximize speed.\nexport function stringToRawPath(d) {\n\tlet a = (d + \"\").replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp) || [], //some authoring programs spit out very small numbers in scientific notation like \"1e-5\", so make sure we round that down to 0 first.\n\t\tpath = [],\n\t\trelativeX = 0,\n\t\trelativeY = 0,\n\t\ttwoThirds = 2 / 3,\n\t\telements = a.length,\n\t\tpoints = 0,\n\t\terrorMessage = \"ERROR: malformed path: \" + d,\n\t\ti, j, x, y, command, isRelative, segment, startX, startY, difX, difY, beziers, prevCommand, flag1, flag2,\n\t\tline = function(sx, sy, ex, ey) {\n\t\t\tdifX = (ex - sx) / 3;\n\t\t\tdifY = (ey - sy) / 3;\n\t\t\tsegment.push(sx + difX, sy + difY, ex - difX, ey - difY, ex, ey);\n\t\t};\n\tif (!d || !isNaN(a[0]) || isNaN(a[1])) {\n\t\tconsole.log(errorMessage);\n\t\treturn path;\n\t}\n\tfor (i = 0; i < elements; i++) {\n\t\tprevCommand = command;\n\t\tif (isNaN(a[i])) {\n\t\t\tcommand = a[i].toUpperCase();\n\t\t\tisRelative = (command !== a[i]); //lower case means relative\n\t\t} else { //commands like \"C\" can be strung together without any new command characters between.\n\t\t\ti--;\n\t\t}\n\t\tx = +a[i + 1];\n\t\ty = +a[i + 2];\n\t\tif (isRelative) {\n\t\t\tx += relativeX;\n\t\t\ty += relativeY;\n\t\t}\n\t\tif (!i) {\n\t\t\tstartX = x;\n\t\t\tstartY = y;\n\t\t}\n\n\t\t// \"M\" (move)\n\t\tif (command === \"M\") {\n\t\t\tif (segment) {\n\t\t\t\tif (segment.length < 8) { //if the path data was funky and just had a M with no actual drawing anywhere, skip it.\n\t\t\t\t\tpath.length -= 1;\n\t\t\t\t} else {\n\t\t\t\t\tpoints += segment.length;\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = startX = x;\n\t\t\trelativeY = startY = y;\n\t\t\tsegment = [x, y];\n\t\t\tpath.push(segment);\n\t\t\ti += 2;\n\t\t\tcommand = \"L\"; //an \"M\" with more than 2 values gets interpreted as \"lineTo\" commands (\"L\").\n\n\t\t// \"C\" (cubic bezier)\n\t\t} else if (command === \"C\") {\n\t\t\tif (!segment) {\n\t\t\t\tsegment = [0, 0];\n\t\t\t}\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\t//note: \"*1\" is just a fast/short way to cast the value as a Number. WAAAY faster in Chrome, slightly slower in Firefox.\n\t\t\tsegment.push(x,\ty, relativeX + a[i + 3] * 1, relativeY + a[i + 4] * 1, (relativeX += a[i + 5] * 1),\t(relativeY += a[i + 6] * 1));\n\t\t\ti += 6;\n\n\t\t// \"S\" (continuation of cubic bezier)\n\t\t} else if (command === \"S\") {\n\t\t\tdifX = relativeX;\n\t\t\tdifY = relativeY;\n\t\t\tif (prevCommand === \"C\" || prevCommand === \"S\") {\n\t\t\t\tdifX += relativeX - segment[segment.length - 4];\n\t\t\t\tdifY += relativeY - segment[segment.length - 3];\n\t\t\t}\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\tsegment.push(difX, difY, x,\ty, (relativeX += a[i + 3] * 1), (relativeY += a[i + 4] * 1));\n\t\t\ti += 4;\n\n\t\t// \"Q\" (quadratic bezier)\n\t\t} else if (command === \"Q\") {\n\t\t\tdifX = relativeX + (x - relativeX) * twoThirds;\n\t\t\tdifY = relativeY + (y - relativeY) * twoThirds;\n\t\t\tif (!isRelative) {\n\t\t\t\trelativeX = relativeY = 0;\n\t\t\t}\n\t\t\trelativeX += a[i + 3] * 1;\n\t\t\trelativeY += a[i + 4] * 1;\n\t\t\tsegment.push(difX, difY, relativeX + (x - relativeX) * twoThirds, relativeY + (y - relativeY) * twoThirds, relativeX, relativeY);\n\t\t\ti += 4;\n\n\t\t// \"T\" (continuation of quadratic bezier)\n\t\t} else if (command === \"T\") {\n\t\t\tdifX = relativeX - segment[segment.length - 4];\n\t\t\tdifY = relativeY - segment[segment.length - 3];\n\t\t\tsegment.push(relativeX + difX, relativeY + difY, x + ((relativeX + difX * 1.5) - x) * twoThirds, y + ((relativeY + difY * 1.5) - y) * twoThirds, (relativeX = x), (relativeY = y));\n\t\t\ti += 2;\n\n\t\t// \"H\" (horizontal line)\n\t\t} else if (command === \"H\") {\n\t\t\tline(relativeX, relativeY, (relativeX = x), relativeY);\n\t\t\ti += 1;\n\n\t\t// \"V\" (vertical line)\n\t\t} else if (command === \"V\") {\n\t\t\t//adjust values because the first (and only one) isn't x in this case, it's y.\n\t\t\tline(relativeX, relativeY, relativeX, (relativeY = x + (isRelative ? relativeY - relativeX : 0)));\n\t\t\ti += 1;\n\n\t\t// \"L\" (line) or \"Z\" (close)\n\t\t} else if (command === \"L\" || command === \"Z\") {\n\t\t\tif (command === \"Z\") {\n\t\t\t\tx = startX;\n\t\t\t\ty = startY;\n\t\t\t\tsegment.closed = true;\n\t\t\t}\n\t\t\tif (command === \"L\" || _abs(relativeX - x) > 0.5 || _abs(relativeY - y) > 0.5) {\n\t\t\t\tline(relativeX, relativeY, x, y);\n\t\t\t\tif (command === \"L\") {\n\t\t\t\t\ti += 2;\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = x;\n\t\t\trelativeY = y;\n\n\t\t// \"A\" (arc)\n\t\t} else if (command === \"A\") {\n\t\t\tflag1 = a[i+4];\n\t\t\tflag2 = a[i+5];\n\t\t\tdifX = a[i+6];\n\t\t\tdifY = a[i+7];\n\t\t\tj = 7;\n\t\t\tif (flag1.length > 1) { // for cases when the flags are merged, like \"a8 8 0 018 8\" (the 0 and 1 flags are WITH the x value of 8, but it could also be \"a8 8 0 01-8 8\" so it may include x or not)\n\t\t\t\tif (flag1.length < 3) {\n\t\t\t\t\tdifY = difX;\n\t\t\t\t\tdifX = flag2;\n\t\t\t\t\tj--;\n\t\t\t\t} else {\n\t\t\t\t\tdifY = flag2;\n\t\t\t\t\tdifX = flag1.substr(2);\n\t\t\t\t\tj-=2;\n\t\t\t\t}\n\t\t\t\tflag2 = flag1.charAt(1);\n\t\t\t\tflag1 = flag1.charAt(0);\n\t\t\t}\n\t\t\tbeziers = arcToSegment(relativeX, relativeY, +a[i+1], +a[i+2], +a[i+3], +flag1, +flag2, (isRelative ? relativeX : 0) + difX*1, (isRelative ? relativeY : 0) + difY*1);\n\t\t\ti += j;\n\t\t\tif (beziers) {\n\t\t\t\tfor (j = 0; j < beziers.length; j++) {\n\t\t\t\t\tsegment.push(beziers[j]);\n\t\t\t\t}\n\t\t\t}\n\t\t\trelativeX = segment[segment.length-2];\n\t\t\trelativeY = segment[segment.length-1];\n\n\t\t} else {\n\t\t\tconsole.log(errorMessage);\n\t\t}\n\t}\n\ti = segment.length;\n\tif (i < 6) { //in case there's odd SVG like a M0,0 command at the very end.\n\t\tpath.pop();\n\t\ti = 0;\n\t} else if (segment[0] === segment[i-2] && segment[1] === segment[i-1]) {\n\t\tsegment.closed = true;\n\t}\n\tpath.totalPoints = points + i;\n\treturn path;\n}\n\n//populates the points array in alternating x/y values (like [x, y, x, y...] instead of individual point objects [{x, y}, {x, y}...] to conserve memory and stay in line with how we're handling segment arrays\nexport function bezierToPoints(x1, y1, x2, y2, x3, y3, x4, y4, threshold, points, index) {\n\tlet x12 = (x1 + x2) / 2,\n\t\ty12 = (y1 + y2) / 2,\n\t\tx23 = (x2 + x3) / 2,\n\t\ty23 = (y2 + y3) / 2,\n\t\tx34 = (x3 + x4) / 2,\n\t\ty34 = (y3 + y4) / 2,\n\t\tx123 = (x12 + x23) / 2,\n\t\ty123 = (y12 + y23) / 2,\n\t\tx234 = (x23 + x34) / 2,\n\t\ty234 = (y23 + y34) / 2,\n\t\tx1234 = (x123 + x234) / 2,\n\t\ty1234 = (y123 + y234) / 2,\n\t\tdx = x4 - x1,\n\t\tdy = y4 - y1,\n\t\td2 = _abs((x2 - x4) * dy - (y2 - y4) * dx),\n\t\td3 = _abs((x3 - x4) * dy - (y3 - y4) * dx),\n\t\tlength;\n\tif (!points) {\n\t\tpoints = [x1, y1, x4, y4];\n\t\tindex = 2;\n\t}\n\tpoints.splice(index || points.length - 2, 0, x1234, y1234);\n\tif ((d2 + d3) * (d2 + d3) > threshold * (dx * dx + dy * dy)) {\n\t\tlength = points.length;\n\t\tbezierToPoints(x1, y1, x12, y12, x123, y123, x1234, y1234, threshold, points, index);\n\t\tbezierToPoints(x1234, y1234, x234, y234, x34, y34, x4, y4, threshold, points, index + 2 + (points.length - length));\n\t}\n\treturn points;\n}\n\n/*\nfunction getAngleBetweenPoints(x0, y0, x1, y1, x2, y2) { //angle between 3 points in radians\n\tvar dx1 = x1 - x0,\n\t\tdy1 = y1 - y0,\n\t\tdx2 = x2 - x1,\n\t\tdy2 = y2 - y1,\n\t\tdx3 = x2 - x0,\n\t\tdy3 = y2 - y0,\n\t\ta = dx1 * dx1 + dy1 * dy1,\n\t\tb = dx2 * dx2 + dy2 * dy2,\n\t\tc = dx3 * dx3 + dy3 * dy3;\n\treturn Math.acos( (a + b - c) / _sqrt(4 * a * b) );\n},\n*/\n\n//pointsToSegment() doesn't handle flat coordinates (where y is always 0) the way we need (the resulting control points are always right on top of the anchors), so this function basically makes the control points go directly up and down, varying in length based on the curviness (more curvy, further control points)\nexport function flatPointsToSegment(points, curviness=1) {\n\tlet x = points[0],\n\t\ty = 0,\n\t\tsegment = [x, y],\n\t\ti = 2;\n\tfor (; i < points.length; i+=2) {\n\t\tsegment.push(\n\t\t\tx,\n\t\t\ty,\n\t\t\tpoints[i],\n\t\t\t(y = (points[i] - x) * curviness / 2),\n\t\t\t(x = points[i]),\n\t\t\t-y\n\t\t);\n\t}\n\treturn segment;\n}\n\n//points is an array of x/y points, like [x, y, x, y, x, y]\nexport function pointsToSegment(points, curviness) {\n\t//points = simplifyPoints(points, tolerance);\n\t_abs(points[0] - points[2]) < 1e-4 && _abs(points[1] - points[3]) < 1e-4 && (points = points.slice(2)); // if the first two points are super close, dump the first one.\n\tlet l = points.length-2,\n\t\tx = +points[0],\n\t\ty = +points[1],\n\t\tnextX = +points[2],\n\t\tnextY = +points[3],\n\t\tsegment = [x, y, x, y],\n\t\tdx2 = nextX - x,\n\t\tdy2 = nextY - y,\n\t\tclosed = Math.abs(points[l] - x) < 0.001 && Math.abs(points[l+1] - y) < 0.001,\n\t\tprevX, prevY, i, dx1, dy1, r1, r2, r3, tl, mx1, mx2, mxm, my1, my2, mym;\n\tif (closed) { // if the start and end points are basically on top of each other, close the segment by adding the 2nd point to the end, and the 2nd-to-last point to the beginning (we'll remove them at the end, but this allows the curvature to look perfect)\n\t\tpoints.push(nextX, nextY);\n\t\tnextX = x;\n\t\tnextY = y;\n\t\tx = points[l-2];\n\t\ty = points[l-1];\n\t\tpoints.unshift(x, y);\n\t\tl+=4;\n\t}\n\tcurviness = (curviness || curviness === 0) ? +curviness : 1;\n\tfor (i = 2; i < l; i+=2) {\n\t\tprevX = x;\n\t\tprevY = y;\n\t\tx = nextX;\n\t\ty = nextY;\n\t\tnextX = +points[i+2];\n\t\tnextY = +points[i+3];\n\t\tif (x === nextX && y === nextY) {\n\t\t\tcontinue;\n\t\t}\n\t\tdx1 = dx2;\n\t\tdy1 = dy2;\n\t\tdx2 = nextX - x;\n\t\tdy2 = nextY - y;\n\t\tr1 = _sqrt(dx1 * dx1 + dy1 * dy1); // r1, r2, and r3 correlate x and y (and z in the future). Basically 2D or 3D hypotenuse\n\t\tr2 = _sqrt(dx2 * dx2 + dy2 * dy2);\n\t\tr3 =  _sqrt((dx2 / r2 + dx1 / r1) ** 2 + (dy2 / r2 + dy1 / r1) ** 2);\n\t\ttl = ((r1 + r2) * curviness * 0.25) / r3;\n\t\tmx1 = x - (x - prevX) * (r1 ? tl / r1 : 0);\n\t\tmx2 = x + (nextX - x) * (r2 ? tl / r2 : 0);\n\t\tmxm = x - (mx1 + (((mx2 - mx1) * ((r1 * 3 / (r1 + r2)) + 0.5) / 4) || 0));\n\t\tmy1 = y - (y - prevY) * (r1 ? tl / r1 : 0);\n\t\tmy2 = y + (nextY - y) * (r2 ? tl / r2 : 0);\n\t\tmym = y - (my1 + (((my2 - my1) * ((r1 * 3 / (r1 + r2)) + 0.5) / 4) || 0));\n\t\tif (x !== prevX || y !== prevY) {\n\t\t\tsegment.push(\n\t\t\t\t_round(mx1 + mxm),  // first control point\n\t\t\t\t_round(my1 + mym),\n\t\t\t\t_round(x),          // anchor\n\t\t\t\t_round(y),\n\t\t\t\t_round(mx2 + mxm),  // second control point\n\t\t\t\t_round(my2 + mym)\n\t\t\t);\n\t\t}\n\t}\n\tx !== nextX || y !== nextY || segment.length < 4 ? segment.push(_round(nextX), _round(nextY), _round(nextX), _round(nextY)) : (segment.length -= 2);\n\tif (segment.length === 2) { // only one point!\n\t\tsegment.push(x, y, x, y, x, y);\n\t} else if (closed) {\n\t\tsegment.splice(0, 6);\n\t\tsegment.length = segment.length - 6;\n\t}\n\treturn segment;\n}\n\n//returns the squared distance between an x/y coordinate and a segment between x1/y1 and x2/y2\nfunction pointToSegDist(x, y, x1, y1, x2, y2) {\n\tlet dx = x2 - x1,\n\t\tdy = y2 - y1,\n\t\tt;\n\tif (dx || dy) {\n\t\tt = ((x - x1) * dx + (y - y1) * dy) / (dx * dx + dy * dy);\n\t\tif (t > 1) {\n\t\t\tx1 = x2;\n\t\t\ty1 = y2;\n\t\t} else if (t > 0) {\n\t\t\tx1 += dx * t;\n\t\t\ty1 += dy * t;\n\t\t}\n\t}\n\treturn (x - x1) ** 2 + (y - y1) ** 2;\n}\n\nfunction simplifyStep(points, first, last, tolerance, simplified) {\n\tlet maxSqDist = tolerance,\n\t\tfirstX = points[first],\n\t\tfirstY = points[first+1],\n\t\tlastX = points[last],\n\t\tlastY = points[last+1],\n\t\tindex, i, d;\n\tfor (i = first + 2; i < last; i += 2) {\n\t\td = pointToSegDist(points[i], points[i+1], firstX, firstY, lastX, lastY);\n\t\tif (d > maxSqDist) {\n\t\t\tindex = i;\n\t\t\tmaxSqDist = d;\n\t\t}\n\t}\n\tif (maxSqDist > tolerance) {\n\t\tindex - first > 2 && simplifyStep(points, first, index, tolerance, simplified);\n\t\tsimplified.push(points[index], points[index+1]);\n\t\tlast - index > 2 && simplifyStep(points, index, last, tolerance, simplified);\n\t}\n}\n\n//points is an array of x/y values like [x, y, x, y, x, y]\nexport function simplifyPoints(points, tolerance) {\n\tlet prevX = parseFloat(points[0]),\n\t\tprevY = parseFloat(points[1]),\n\t\ttemp = [prevX, prevY],\n\t\tl = points.length - 2,\n\t\ti, x, y, dx, dy, result, last;\n\ttolerance = (tolerance || 1) ** 2;\n\tfor (i = 2; i < l; i += 2) {\n\t\tx = parseFloat(points[i]);\n\t\ty = parseFloat(points[i+1]);\n\t\tdx = prevX - x;\n\t\tdy = prevY - y;\n\t\tif (dx * dx + dy * dy > tolerance) {\n\t\t\ttemp.push(x, y);\n\t\t\tprevX = x;\n\t\t\tprevY = y;\n\t\t}\n\t}\n\ttemp.push(parseFloat(points[l]), parseFloat(points[l+1]));\n\tlast = temp.length - 2;\n\tresult = [temp[0], temp[1]];\n\tsimplifyStep(temp, 0, last, tolerance, result);\n\tresult.push(temp[last], temp[last+1]);\n\treturn result;\n}\n\nfunction getClosestProgressOnBezier(iterations, px, py, start, end, slices, x0, y0, x1, y1, x2, y2, x3, y3) {\n\tlet inc = (end - start) / slices,\n\t\tbest = 0,\n\t\tt = start,\n\t\tx, y, d, dx, dy, inv;\n\t_bestDistance = _largeNum;\n\twhile (t <= end) {\n\t\tinv = 1 - t;\n\t\tx = inv * inv * inv * x0 + 3 * inv * inv * t * x1 + 3 * inv * t * t * x2 + t * t * t * x3;\n\t\ty = inv * inv * inv * y0 + 3 * inv * inv * t * y1 + 3 * inv * t * t * y2 + t * t * t * y3;\n\t\tdx = x - px;\n\t\tdy = y - py;\n\t\td = dx * dx + dy * dy;\n\t\tif (d < _bestDistance) {\n\t\t\t_bestDistance = d;\n\t\t\tbest = t;\n\t\t}\n\t\tt += inc;\n\t}\n\treturn (iterations > 1) ? getClosestProgressOnBezier(iterations - 1, px, py, Math.max(best - inc, 0), Math.min(best + inc, 1), slices, x0, y0, x1, y1, x2, y2, x3, y3) : best;\n}\n\nexport function getClosestData(rawPath, x, y, slices) { //returns an object with the closest j, i, and t (j is the segment index, i is the index of the point in that segment, and t is the time/progress along that bezier)\n\tlet closest = {j:0, i:0, t:0},\n\t\tbestDistance = _largeNum,\n\t\ti, j, t, segment;\n\tfor (j = 0; j < rawPath.length; j++) {\n\t\tsegment = rawPath[j];\n\t\tfor (i = 0; i < segment.length; i+=6) {\n\t\t\tt = getClosestProgressOnBezier(1, x, y, 0, 1, slices || 20, segment[i], segment[i+1], segment[i+2], segment[i+3], segment[i+4], segment[i+5], segment[i+6], segment[i+7]);\n\t\t\tif (bestDistance > _bestDistance) {\n\t\t\t\tbestDistance = _bestDistance;\n\t\t\t\tclosest.j = j;\n\t\t\t\tclosest.i = i;\n\t\t\t\tclosest.t = t;\n\t\t\t}\n\t\t}\n\t}\n\treturn closest;\n}\n\n//subdivide a Segment closest to a specific x,y coordinate\nexport function subdivideSegmentNear(x, y, segment, slices, iterations) {\n\tlet l = segment.length,\n\t\tbestDistance = _largeNum,\n\t\tbestT = 0,\n\t\tbestSegmentIndex = 0,\n\t\tt, i;\n\tslices = slices || 20;\n\titerations = iterations || 3;\n\tfor (i = 0; i < l; i += 6) {\n\t\tt = getClosestProgressOnBezier(1, x, y, 0, 1, slices, segment[i], segment[i+1], segment[i+2], segment[i+3], segment[i+4], segment[i+5], segment[i+6], segment[i+7]);\n\t\tif (bestDistance > _bestDistance) {\n\t\t\tbestDistance = _bestDistance;\n\t\t\tbestT = t;\n\t\t\tbestSegmentIndex = i;\n\t\t}\n\t}\n\tt = getClosestProgressOnBezier(iterations, x, y, bestT - 0.05, bestT + 0.05, slices, segment[bestSegmentIndex], segment[bestSegmentIndex+1], segment[bestSegmentIndex+2], segment[bestSegmentIndex+3], segment[bestSegmentIndex+4], segment[bestSegmentIndex+5], segment[bestSegmentIndex+6], segment[bestSegmentIndex+7]);\n\tsubdivideSegment(segment, bestSegmentIndex, t);\n\treturn bestSegmentIndex + 6;\n}\n\n/*\nTakes any of the following and converts it to an all Cubic Bezier SVG data string:\n- A <path> data string like \"M0,0 L2,4 v20,15 H100\"\n- A RawPath, like [[x, y, x, y, x, y, x, y][[x, y, x, y, x, y, x, y]]\n- A Segment, like [x, y, x, y, x, y, x, y]\n\nNote: all numbers are rounded down to the closest 0.001 to minimize memory, maximize speed, and avoid odd numbers like 1e-13\n*/\nexport function rawPathToString(rawPath) {\n\tif (_isNumber(rawPath[0])) { //in case a segment is passed in instead\n\t\trawPath = [rawPath];\n\t}\n\tlet result = \"\",\n\t\tl = rawPath.length,\n\t\tsl, s, i, segment;\n\tfor (s = 0; s < l; s++) {\n\t\tsegment = rawPath[s];\n\t\tresult += \"M\" + _round(segment[0]) + \",\" + _round(segment[1]) + \" C\";\n\t\tsl = segment.length;\n\t\tfor (i = 2; i < sl; i++) {\n\t\t\tresult += _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i++]) + \" \" + _round(segment[i++]) + \",\" + _round(segment[i]) + \" \";\n\t\t}\n\t\tif (segment.closed) {\n\t\t\tresult += \"z\";\n\t\t}\n\t}\n\treturn result;\n}\n\n/*\n// takes a segment with coordinates [x, y, x, y, ...] and converts the control points into angles and lengths [x, y, angle, length, angle, length, x, y, angle, length, ...] so that it animates more cleanly and avoids odd breaks/kinks. For example, if you animate from 1 o'clock to 6 o'clock, it'd just go directly/linearly rather than around. So the length would be very short in the middle of the tween.\nexport function cpCoordsToAngles(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tx, y, i;\n\tfor (i = 0; i < segment.length; i+=6) {\n\t\tx = segment[i+2] - segment[i];\n\t\ty = segment[i+3] - segment[i+1];\n\t\tresult[i+2] = Math.atan2(y, x);\n\t\tresult[i+3] = Math.sqrt(x * x + y * y);\n\t\tx = segment[i+6] - segment[i+4];\n\t\ty = segment[i+7] - segment[i+5];\n\t\tresult[i+4] = Math.atan2(y, x);\n\t\tresult[i+5] = Math.sqrt(x * x + y * y);\n\t}\n\treturn result;\n}\n\n// takes a segment that was converted with cpCoordsToAngles() to have angles and lengths instead of coordinates for the control points, and converts it BACK into coordinates.\nexport function cpAnglesToCoords(segment, copy) {\n\tvar result = copy ? segment.slice(0) : segment,\n\t\tlength = segment.length,\n\t\trnd = 1000,\n\t\tangle, l, i, j;\n\tfor (i = 0; i < length; i+=6) {\n\t\tangle = segment[i+2];\n\t\tl = segment[i+3]; //length\n\t\tresult[i+2] = (((segment[i] + Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+3] = (((segment[i+1] + Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t\tangle = segment[i+4];\n\t\tl = segment[i+5]; //length\n\t\tresult[i+4] = (((segment[i+6] - Math.cos(angle) * l) * rnd) | 0) / rnd;\n\t\tresult[i+5] = (((segment[i+7] - Math.sin(angle) * l) * rnd) | 0) / rnd;\n\t}\n\treturn result;\n}\n\n//adds an \"isSmooth\" array to each segment and populates it with a boolean value indicating whether or not it's smooth (the control points have basically the same slope). For any smooth control points, it converts the coordinates into angle (x, in radians) and length (y) and puts them into the same index value in a smoothData array.\nexport function populateSmoothData(rawPath) {\n\tlet j = rawPath.length,\n\t\tsmooth, segment, x, y, x2, y2, i, l, a, a2, isSmooth, smoothData;\n\twhile (--j > -1) {\n\t\tsegment = rawPath[j];\n\t\tisSmooth = segment.isSmooth = segment.isSmooth || [0, 0, 0, 0];\n\t\tsmoothData = segment.smoothData = segment.smoothData || [0, 0, 0, 0];\n\t\tisSmooth.length = 4;\n\t\tl = segment.length - 2;\n\t\tfor (i = 6; i < l; i += 6) {\n\t\t\tx = segment[i] - segment[i - 2];\n\t\t\ty = segment[i + 1] - segment[i - 1];\n\t\t\tx2 = segment[i + 2] - segment[i];\n\t\t\ty2 = segment[i + 3] - segment[i + 1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tsmooth = (Math.abs(a - a2) < 0.09);\n\t\t\tif (smooth) {\n\t\t\t\tsmoothData[i - 2] = a;\n\t\t\t\tsmoothData[i + 2] = a2;\n\t\t\t\tsmoothData[i - 1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[i + 3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t}\n\t\t\tisSmooth.push(smooth, smooth, 0, 0, smooth, smooth);\n\t\t}\n\t\t//if the first and last points are identical, check to see if there's a smooth transition. We must handle this a bit differently due to their positions in the array.\n\t\tif (segment[l] === segment[0] && segment[l+1] === segment[1]) {\n\t\t\tx = segment[0] - segment[l-2];\n\t\t\ty = segment[1] - segment[l-1];\n\t\t\tx2 = segment[2] - segment[0];\n\t\t\ty2 = segment[3] - segment[1];\n\t\t\ta = _atan2(y, x);\n\t\t\ta2 = _atan2(y2, x2);\n\t\t\tif (Math.abs(a - a2) < 0.09) {\n\t\t\t\tsmoothData[l-2] = a;\n\t\t\t\tsmoothData[2] = a2;\n\t\t\t\tsmoothData[l-1] = _sqrt(x * x + y * y);\n\t\t\t\tsmoothData[3] = _sqrt(x2 * x2 + y2 * y2);\n\t\t\t\tisSmooth[l-2] = isSmooth[l-1] = true; //don't change indexes 2 and 3 because we'll trigger everything from the END, and this will optimize file size a bit.\n\t\t\t}\n\t\t}\n\t}\n\treturn rawPath;\n}\nexport function pointToScreen(svgElement, point) {\n\tif (arguments.length < 2) { //by default, take the first set of coordinates in the path as the point\n\t\tlet rawPath = getRawPath(svgElement);\n\t\tpoint = svgElement.ownerSVGElement.createSVGPoint();\n\t\tpoint.x = rawPath[0][0];\n\t\tpoint.y = rawPath[0][1];\n\t}\n\treturn point.matrixTransform(svgElement.getScreenCTM());\n}\n// takes a <path> and normalizes all of its coordinates to values between 0 and 1\nexport function normalizePath(path) {\n  path = gsap.utils.toArray(path);\n  if (!path[0].hasAttribute(\"d\")) {\n    path = gsap.utils.toArray(path[0].children);\n  }\n  if (path.length > 1) {\n    path.forEach(normalizePath);\n    return path;\n  }\n  let _svgPathExp = /[achlmqstvz]|(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)[0-9]/ig,\n      _scientific = /[\\+\\-]?\\d*\\.?\\d+e[\\+\\-]?\\d+/ig,\n      d = path[0].getAttribute(\"d\"),\n      a = d.replace(_scientific, m => { let n = +m; return (n < 0.0001 && n > -0.0001) ? 0 : n; }).match(_svgPathExp),\n      nums = a.filter(n => !isNaN(n)).map(n => +n),\n      normalize = gsap.utils.normalize(Math.min(...nums), Math.max(...nums)),\n      finals = a.map(val => isNaN(val) ? val : normalize(+val)),\n      s = \"\",\n      prevWasCommand;\n  finals.forEach((value, i) => {\n    let isCommand = isNaN(value)\n    s += (isCommand && i ? \" \" : prevWasCommand || !i ? \"\" : \",\") + value;\n    prevWasCommand = isCommand;\n  });\n  path[0].setAttribute(\"d\", s);\n}\n*/", "/*!\n * CustomEase 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { stringToRawPath, rawPathToString, transformRawPath } from \"./utils/paths.js\";\n\nlet gsap, _coreInitted,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_initCore = () => {\n\t\tgsap = _getGSAP();\n\t\tif (gsap) {\n\t\t\tgsap.registerEase(\"_CE\", CustomEase.create);\n\t\t\t_coreInitted = 1;\n\t\t} else {\n\t\t\tconsole.warn(\"Please gsap.registerPlugin(CustomEase)\");\n\t\t}\n\t},\n\t_bigNum = 1e20,\n\t_round = value => ~~(value * 1000 + (value < 0 ? -.5 : .5)) / 1000,\n\t_bonusValidated = 1, //<name>CustomEase</name>\n\t_numExp = /[-+=.]*\\d+[.e\\-+]*\\d*[e\\-+]*\\d*/gi, //finds any numbers, including ones that start with += or -=, negative numbers, and ones in scientific notation like 1e-8.\n\t_needsParsingExp = /[cLlsSaAhHvVtTqQ]/g,\n\t_findMinimum = values => {\n\t\tlet l = values.length,\n\t\t\tmin = _bigNum,\n\t\t\ti;\n\t\tfor (i = 1; i < l; i += 6) {\n\t\t\t+values[i] < min && (min = +values[i]);\n\t\t}\n\t\treturn min;\n\t},\n\t//takes all the points and translates/scales them so that the x starts at 0 and ends at 1.\n\t_normalize = (values, height, originY) => {\n\t\tif (!originY && originY !== 0) {\n\t\t\toriginY = Math.max(+values[values.length-1], +values[1]);\n\t\t}\n\t\tlet tx = +values[0] * -1,\n\t\t\tty = -originY,\n\t\t\tl = values.length,\n\t\t\tsx = 1 / (+values[l - 2] + tx),\n\t\t\tsy = -height || ((Math.abs(+values[l - 1] - +values[1]) < 0.01 * (+values[l - 2] - +values[0])) ? _findMinimum(values) + ty : +values[l - 1] + ty),\n\t\t\ti;\n\t\tif (sy) { //typically y ends at 1 (so that the end values are reached)\n\t\t\tsy = 1 / sy;\n\t\t} else { //in case the ease returns to its beginning value, scale everything proportionally\n\t\t\tsy = -sx;\n\t\t}\n\t\tfor (i = 0; i < l; i += 2) {\n\t\t\tvalues[i] = (+values[i] + tx) * sx;\n\t\t\tvalues[i + 1] = (+values[i + 1] + ty) * sy;\n\t\t}\n\t},\n\t//note that this function returns point objects like {x, y} rather than working with segments which are arrays with alternating x, y values as in the similar function in paths.js\n\t_bezierToPoints = function (x1, y1, x2, y2, x3, y3, x4, y4, threshold, points, index) {\n\t\tlet x12 = (x1 + x2) / 2,\n\t\t\ty12 = (y1 + y2) / 2,\n\t\t\tx23 = (x2 + x3) / 2,\n\t\t\ty23 = (y2 + y3) / 2,\n\t\t\tx34 = (x3 + x4) / 2,\n\t\t\ty34 = (y3 + y4) / 2,\n\t\t\tx123 = (x12 + x23) / 2,\n\t\t\ty123 = (y12 + y23) / 2,\n\t\t\tx234 = (x23 + x34) / 2,\n\t\t\ty234 = (y23 + y34) / 2,\n\t\t\tx1234 = (x123 + x234) / 2,\n\t\t\ty1234 = (y123 + y234) / 2,\n\t\t\tdx = x4 - x1,\n\t\t\tdy = y4 - y1,\n\t\t\td2 = Math.abs((x2 - x4) * dy - (y2 - y4) * dx),\n\t\t\td3 = Math.abs((x3 - x4) * dy - (y3 - y4) * dx),\n\t\t\tlength;\n\t\tif (!points) {\n\t\t\tpoints = [{x: x1, y: y1}, {x: x4, y: y4}];\n\t\t\tindex = 1;\n\t\t}\n\t\tpoints.splice(index || points.length - 1, 0, {x: x1234, y: y1234});\n\t\tif ((d2 + d3) * (d2 + d3) > threshold * (dx * dx + dy * dy)) {\n\t\t\tlength = points.length;\n\t\t\t_bezierToPoints(x1, y1, x12, y12, x123, y123, x1234, y1234, threshold, points, index);\n\t\t\t_bezierToPoints(x1234, y1234, x234, y234, x34, y34, x4, y4, threshold, points, index + 1 + (points.length - length));\n\t\t}\n\t\treturn points;\n\t};\n\nexport class CustomEase {\n\n\tconstructor(id, data, config) {\n\t\t_coreInitted || _initCore();\n\t\tthis.id = id;\n\t\t_bonusValidated && this.setData(data, config);\n\t}\n\n\tsetData(data, config) {\n\t\tconfig = config || {};\n\t\tdata = data || \"0,0,1,1\";\n\t\tlet values = data.match(_numExp),\n\t\t\tclosest = 1,\n\t\t\tpoints = [],\n\t\t\tlookup = [],\n\t\t\tprecision = config.precision || 1,\n\t\t\tfast = (precision <= 1),\n\t\t\tl, a1, a2, i, inc, j, point, prevPoint, p;\n\t\tthis.data = data;\n\t\tif (_needsParsingExp.test(data) || (~data.indexOf(\"M\") && data.indexOf(\"C\") < 0)) {\n\t\t\tvalues = stringToRawPath(data)[0];\n\t\t}\n\t\tl = values.length;\n\t\tif (l === 4) {\n\t\t\tvalues.unshift(0, 0);\n\t\t\tvalues.push(1, 1);\n\t\t\tl = 8;\n\t\t} else if ((l - 2) % 6) {\n\t\t\tthrow \"Invalid CustomEase\";\n\t\t}\n\t\tif (+values[0] !== 0 || +values[l - 2] !== 1) {\n\t\t\t_normalize(values, config.height, config.originY);\n\t\t}\n\t\tthis.segment = values;\n\t\tfor (i = 2; i < l; i += 6) {\n\t\t\ta1 = {x: +values[i - 2], y: +values[i - 1]};\n\t\t\ta2 = {x: +values[i + 4], y: +values[i + 5]};\n\t\t\tpoints.push(a1, a2);\n\t\t\t_bezierToPoints(a1.x, a1.y, +values[i], +values[i + 1], +values[i + 2], +values[i + 3], a2.x, a2.y, 1 / (precision * 200000), points, points.length - 1);\n\t\t}\n\t\tl = points.length;\n\t\tfor (i = 0; i < l; i++) {\n\t\t\tpoint = points[i];\n\t\t\tprevPoint = points[i - 1] || point;\n\t\t\tif ((point.x > prevPoint.x || (prevPoint.y !== point.y && prevPoint.x === point.x) || point === prevPoint) && point.x <= 1) { //if a point goes BACKWARD in time or is a duplicate, just drop it. Also it shouldn't go past 1 on the x axis, as could happen in a string like \"M0,0 C0,0 0.12,0.68 0.18,0.788 0.195,0.845 0.308,1 0.32,1 0.403,1.005 0.398,1 0.5,1 0.602,1 0.816,1.005 0.9,1 0.91,1 0.948,0.69 0.962,0.615 1.003,0.376 1,0 1,0\".\n\t\t\t\tprevPoint.cx = point.x - prevPoint.x; //change in x between this point and the next point (performance optimization)\n\t\t\t\tprevPoint.cy = point.y - prevPoint.y;\n\t\t\t\tprevPoint.n = point;\n\t\t\t\tprevPoint.nx = point.x; //next point's x value (performance optimization, making lookups faster in getRatio()). Remember, the lookup will always land on a spot where it's either this point or the very next one (never beyond that)\n\t\t\t\tif (fast && i > 1 && Math.abs(prevPoint.cy / prevPoint.cx - points[i - 2].cy / points[i - 2].cx) > 2) { //if there's a sudden change in direction, prioritize accuracy over speed. Like a bounce ease - you don't want to risk the sampling chunks landing on each side of the bounce anchor and having it clipped off.\n\t\t\t\t\tfast = 0;\n\t\t\t\t}\n\t\t\t\tif (prevPoint.cx < closest) {\n\t\t\t\t\tif (!prevPoint.cx) {\n\t\t\t\t\t\tprevPoint.cx = 0.001; //avoids math problems in getRatio() (dividing by zero)\n\t\t\t\t\t\tif (i === l - 1) { //in case the final segment goes vertical RIGHT at the end, make sure we end at the end.\n\t\t\t\t\t\t\tprevPoint.x -= 0.001;\n\t\t\t\t\t\t\tclosest = Math.min(closest, 0.001);\n\t\t\t\t\t\t\tfast = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tclosest = prevPoint.cx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tpoints.splice(i--, 1);\n\t\t\t\tl--;\n\t\t\t}\n\t\t}\n\t\tl = (1 / closest + 1) | 0;\n\t\tinc = 1 / l;\n\t\tj = 0;\n\t\tpoint = points[0];\n\t\tif (fast) {\n\t\t\tfor (i = 0; i < l; i++) { //for fastest lookups, we just sample along the path at equal x (time) distance. Uses more memory and is slightly less accurate for anchors that don't land on the sampling points, but for the vast majority of eases it's excellent (and fast).\n\t\t\t\tp = i * inc;\n\t\t\t\tif (point.nx < p) {\n\t\t\t\t\tpoint = points[++j];\n\t\t\t\t}\n\t\t\t\ta1 = point.y + ((p - point.x) / point.cx) * point.cy;\n\t\t\t\tlookup[i] = {x: p, cx: inc, y: a1, cy: 0, nx: 9};\n\t\t\t\tif (i) {\n\t\t\t\t\tlookup[i - 1].cy = a1 - lookup[i - 1].y;\n\t\t\t\t}\n\t\t\t}\n\t\t\tj = points[points.length - 1];\n\t\t\tlookup[l - 1].cy = j.y - a1;\n\t\t\tlookup[l - 1].cx = j.x - lookup[lookup.length - 1].x; //make sure it lands EXACTLY where it should. Otherwise, it might be something like 0.9999999999 instead of 1.\n\t\t} else { //this option is more accurate, ensuring that EVERY anchor is hit perfectly. Clipping across a bounce, for example, would never happen.\n\t\t\tfor (i = 0; i < l; i++) { //build a lookup table based on the smallest distance so that we can instantly find the appropriate point (well, it'll either be that point or the very next one). We'll look up based on the linear progress. So it's it's 0.5 and the lookup table has 100 elements, it'd be like lookup[Math.floor(0.5 * 100)]\n\t\t\t\tif (point.nx < i * inc) {\n\t\t\t\t\tpoint = points[++j];\n\t\t\t\t}\n\t\t\t\tlookup[i] = point;\n\t\t\t}\n\n\t\t\tif (j < points.length - 1) {\n\t\t\t\tlookup[i-1] = points[points.length-2];\n\t\t\t}\n\t\t}\n\t\t//this._calcEnd = (points[points.length-1].y !== 1 || points[0].y !== 0); //ensures that we don't run into floating point errors. As long as we're starting at 0 and ending at 1, tell GSAP to skip the final calculation and use 0/1 as the factor.\n\n\t\tthis.ease = p => {\n\t\t\tlet point = lookup[(p * l) | 0] || lookup[l - 1];\n\t\t\tif (point.nx < p) {\n\t\t\t\tpoint = point.n;\n\t\t\t}\n\t\t\treturn point.y + ((p - point.x) / point.cx) * point.cy;\n\t\t};\n\n\t\tthis.ease.custom = this;\n\n\t\tthis.id && gsap && gsap.registerEase(this.id, this.ease);\n\n\t\treturn this;\n\t}\n\n\tgetSVGData(config) {\n\t\treturn CustomEase.getSVGData(this, config);\n\t}\n\n\tstatic create(id, data, config) {\n\t\treturn (new CustomEase(id, data, config)).ease;\n\t}\n\n\tstatic register(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t}\n\n\tstatic get(id) {\n\t\treturn gsap.parseEase(id);\n\t}\n\n\tstatic getSVGData(ease, config) {\n\t\tconfig = config || {};\n\t\tlet width = config.width || 100,\n\t\t\theight = config.height || 100,\n\t\t\tx = config.x || 0,\n\t\t\ty = (config.y || 0) + height,\n\t\t\te = gsap.utils.toArray(config.path)[0],\n\t\t\ta, slope, i, inc, tx, ty, precision, threshold, prevX, prevY;\n\t\tif (config.invert) {\n\t\t\theight = -height;\n\t\t\ty = 0;\n\t\t}\n\t\tif (typeof(ease) === \"string\") {\n\t\t\tease = gsap.parseEase(ease);\n\t\t}\n\t\tif (ease.custom) {\n\t\t\tease = ease.custom;\n\t\t}\n\t\tif (ease instanceof CustomEase) {\n\t\t\ta = rawPathToString(transformRawPath([ease.segment], width, 0, 0, -height, x, y));\n\t\t} else {\n\t\t\ta = [x, y];\n\t\t\tprecision = Math.max(5, (config.precision || 1) * 200);\n\t\t\tinc = 1 / precision;\n\t\t\tprecision += 2;\n\t\t\tthreshold = 5 / precision;\n\t\t\tprevX = _round(x + inc * width);\n\t\t\tprevY = _round(y + ease(inc) * -height);\n\t\t\tslope = (prevY - y) / (prevX - x);\n\t\t\tfor (i = 2; i < precision; i++) {\n\t\t\t\ttx = _round(x + i * inc * width);\n\t\t\t\tty = _round(y + ease(i * inc) * -height);\n\t\t\t\tif (Math.abs((ty - prevY) / (tx - prevX) - slope) > threshold || i === precision - 1) { //only add points when the slope changes beyond the threshold\n\t\t\t\t\ta.push(prevX, prevY);\n\t\t\t\t\tslope = (ty - prevY) / (tx - prevX);\n\t\t\t\t}\n\t\t\t\tprevX = tx;\n\t\t\t\tprevY = ty;\n\t\t\t}\n\t\t\ta = \"M\" + a.join(\",\");\n\t\t}\n\t\te && e.setAttribute(\"d\", a);\n\t\treturn a;\n\t}\n\n}\n\nCustomEase.version = \"3.13.0\";\nCustomEase.headless = true;\n\n_getGSAP() && gsap.registerPlugin(CustomEase);\n\nexport { CustomEase as default };"], "names": ["_round", "value", "Math", "round", "_svgPathExp", "_scientific", "_DEG2RAD", "PI", "_sin", "sin", "_cos", "cos", "_abs", "abs", "_sqrt", "sqrt", "arcToSegment", "lastX", "lastY", "rx", "ry", "angle", "largeArcFlag", "sweepFlag", "x", "y", "angleRad", "cosAngle", "sinAngle", "TWOPI", "dx2", "dy2", "x1", "y1", "x1_sq", "y1_sq", "radiiCheck", "rx_sq", "ry_sq", "sq", "coef", "cx1", "cy1", "cx", "cy", "ux", "uy", "vx", "vy", "temp", "angleStart", "acos", "angleExtent", "isNaN", "i", "segments", "ceil", "rawPath", "angleIncrement", "controlLength", "ma", "mb", "mc", "md", "push", "length", "stringToRawPath", "d", "line", "sx", "sy", "ex", "ey", "difX", "difY", "segment", "j", "command", "isRelative", "startX", "startY", "beziers", "prevCommand", "flag1", "flag2", "a", "replace", "m", "n", "match", "path", "relativeX", "relativeY", "elements", "points", "errorMessage", "console", "log", "toUpperCase", "closed", "substr", "char<PERSON>t", "pop", "totalPoints", "_getGSAP", "gsap", "window", "registerPlugin", "_initCore", "registerEase", "CustomEase", "create", "_coreInitted", "warn", "_bezierToPoints", "x2", "y2", "x3", "y3", "x4", "y4", "threshold", "index", "x12", "y12", "x23", "y23", "x34", "y34", "x123", "y123", "x234", "y234", "x1234", "y1234", "dx", "dy", "d2", "d3", "splice", "_bonusValidated", "_needsParsingExp", "setData", "data", "config", "l", "a1", "a2", "inc", "point", "prevPoint", "p", "values", "_numExp", "closest", "lookup", "precision", "fast", "test", "indexOf", "unshift", "_normalize", "height", "originY", "max", "tx", "ty", "_findMinimum", "min", "nx", "ease", "custom", "this", "id", "getSVGData", "register", "core", "get", "parseEase", "slope", "prevX", "prevY", "width", "e", "utils", "toArray", "invert", "rawPathToString", "_isNumber", "sl", "s", "result", "transformRawPath", "b", "c", "_dirty", "join", "setAttribute", "version", "headless"], "mappings": ";;;;;;;;;6MA6BU,SAATA,EAASC,UAAUC,KAAKC,MAFT,IAEeF,GAFf,KAEwD,MAnBpEG,EAAc,mDAEjBC,EAAc,gCAEdC,EAAWJ,KAAKK,GAAK,IAErBC,EAAON,KAAKO,IACZC,EAAOR,KAAKS,IACZC,EAAOV,KAAKW,IACZC,EAAQZ,KAAKa,KAqjBd,SAASC,aAAaC,EAAOC,EAAOC,EAAIC,EAAIC,EAAOC,EAAcC,EAAWC,EAAGC,MAC1ER,IAAUO,GAAKN,IAAUO,GAG7BN,EAAKP,EAAKO,GACVC,EAAKR,EAAKQ,OACNM,EAAYL,EAAQ,IAAOf,EAC9BqB,EAAWjB,EAAKgB,GAChBE,EAAWpB,EAAKkB,GAChBnB,EAAKL,KAAKK,GACVsB,EAAa,EAALtB,EACRuB,GAAOb,EAAQO,GAAK,EACpBO,GAAOb,EAAQO,GAAK,EACpBO,EAAML,EAAWG,EAAMF,EAAWG,EAClCE,GAAOL,EAAWE,EAAMH,EAAWI,EACnCG,EAAQF,EAAKA,EACbG,EAAQF,EAAKA,EACbG,EAAaF,GAASf,EAAKA,GAAMgB,GAASf,EAAKA,GAC/B,EAAbgB,IACHjB,EAAKL,EAAMsB,GAAcjB,EACzBC,EAAKN,EAAMsB,GAAchB,OAEtBiB,EAAQlB,EAAKA,EAChBmB,EAAQlB,EAAKA,EACbmB,GAAOF,EAAQC,EAAUD,EAAQF,EAAUG,EAAQJ,IAAYG,EAAQF,EAAUG,EAAQJ,GACtFK,EAAK,IACRA,EAAK,OAEFC,GAASlB,IAAiBC,GAAc,EAAI,GAAKT,EAAMyB,GAC1DE,EAAetB,EAAKc,EAAMb,EAApBoB,EACNE,GAAgBtB,EAAKY,EAAMb,EAArBqB,EAGNG,EAAYhB,EAAWc,EAAMb,EAAWc,GAFjCzB,EAAQO,GAAK,EAGpBoB,EAAYhB,EAAWa,EAAMd,EAAWe,GAFjCxB,EAAQO,GAAK,EAGpBoB,GAAMb,EAAKS,GAAOtB,EAClB2B,GAAMb,EAAKS,GAAOtB,EAClB2B,IAAOf,EAAKS,GAAOtB,EACnB6B,IAAOf,EAAKS,GAAOtB,EACnB6B,EAAOJ,EAAKA,EAAKC,EAAKA,EACtBI,GAAeJ,EAAK,GAAM,EAAI,GAAK5C,KAAKiD,KAAKN,EAAK/B,EAAMmC,IACxDG,GAAgBP,EAAKG,EAAKF,EAAKC,EAAK,GAAM,EAAI,GAAK7C,KAAKiD,MAAMN,EAAKE,EAAKD,EAAKE,GAAMlC,EAAMmC,GAAQF,EAAKA,EAAKC,EAAKA,KACjHK,MAAMD,KAAiBA,EAAc7C,IAChCgB,GAA2B,EAAd6B,EACjBA,GAAevB,EACLN,GAAa6B,EAAc,IACrCA,GAAevB,GAEhBqB,GAAcrB,EACduB,GAAevB,MASdyB,EARGC,EAAWrD,KAAKsD,KAAK5C,EAAKwC,IAAgBvB,EAAQ,IACrD4B,EAAU,GACVC,EAAiBN,EAAcG,EAC/BI,EAAgB,EAAI,EAAInD,EAAKkD,EAAiB,IAAM,EAAIhD,EAAKgD,EAAiB,IAC9EE,EAAKjC,EAAWR,EAChB0C,EAAKjC,EAAWT,EAChB2C,EAAKlC,GAAYR,EACjB2C,EAAKpC,EAAWP,MAEZkC,EAAI,EAAGA,EAAIC,EAAUD,IAEzBtB,EAAKtB,EADLW,EAAQ6B,EAAaI,EAAII,GAEzBzB,EAAKzB,EAAKa,GACVwB,EAAKnC,EAAKW,GAASqC,GACnBZ,EAAKtC,EAAKa,GACVoC,EAAQO,KAAKhC,EAAK2B,EAAgB1B,EAAIA,EAAK0B,EAAgB3B,EAAIa,EAAKc,EAAgBb,EAAIA,EAAKa,EAAgBd,EAAIA,EAAIC,OAGjHQ,EAAI,EAAGA,EAAIG,EAAQQ,OAAQX,GAAG,EAClCtB,EAAKyB,EAAQH,GACbrB,EAAKwB,EAAQH,EAAE,GACfG,EAAQH,GAAKtB,EAAK4B,EAAK3B,EAAK6B,EAAKnB,EACjCc,EAAQH,EAAE,GAAKtB,EAAK6B,EAAK5B,EAAK8B,EAAKnB,SAEpCa,EAAQH,EAAE,GAAK9B,EACfiC,EAAQH,EAAE,GAAK7B,EACRgC,GAID,SAASS,gBAAgBC,GAUvB,SAAPC,GAAgBC,EAAIC,EAAIC,EAAIC,GAC3BC,GAAQF,EAAKF,GAAM,EACnBK,GAAQF,EAAKF,GAAM,EACnBK,EAAQX,KAAKK,EAAKI,EAAMH,EAAKI,EAAMH,EAAKE,EAAMD,EAAKE,EAAMH,EAAIC,OAJ9DlB,EAAGsB,EAAGpD,EAAGC,EAAGoD,EAASC,EAAYH,EAASI,EAAQC,EAAQP,EAAMC,EAAMO,EAASC,EAAaC,EAAOC,EARhGC,GAAKlB,EAAI,IAAImB,QAAQjF,EAAa,SAAAkF,OAAWC,GAAKD,SAAWC,EAAI,OAAe,KAALA,EAAe,EAAIA,IAAMC,MAAMrF,IAAgB,GAC7HsF,EAAO,GACPC,EAAY,EACZC,EAAY,EAEZC,EAAWR,EAAEpB,OACb6B,EAAS,EACTC,EAAe,0BAA4B5B,MAOvCA,IAAMd,MAAMgC,EAAE,KAAOhC,MAAMgC,EAAE,WACjCW,QAAQC,IAAIF,GACLL,MAEHpC,EAAI,EAAGA,EAAIuC,EAAUvC,OACzB4B,EAAcL,EACVxB,MAAMgC,EAAE/B,IAEXwB,GADAD,EAAUQ,EAAE/B,GAAG4C,iBACWb,EAAE/B,GAE5BA,IAED9B,GAAK6D,EAAE/B,EAAI,GACX7B,GAAK4D,EAAE/B,EAAI,GACPwB,IACHtD,GAAKmE,EACLlE,GAAKmE,GAEDtC,IACJyB,EAASvD,EACTwD,EAASvD,GAIM,MAAZoD,EACCF,IACCA,EAAQV,OAAS,IACpByB,EAAKzB,OAEL6B,GAAUnB,EAAQV,QAGpB0B,EAAYZ,EAASvD,EACrBoE,EAAYZ,EAASvD,EACrBkD,EAAU,CAACnD,EAAGC,GACdiE,EAAK1B,KAAKW,GACVrB,GAAK,EACLuB,EAAU,SAGJ,GAAgB,MAAZA,EAILC,IACJa,EAAYC,EAAY,IAHxBjB,EADIA,GACM,CAAC,EAAG,IAMPX,KAAKxC,EAAGC,EAAGkE,EAAuB,EAAXN,EAAE/B,EAAI,GAAQsC,EAAuB,EAAXP,EAAE/B,EAAI,GAASqC,GAAwB,EAAXN,EAAE/B,EAAI,GAAUsC,GAAwB,EAAXP,EAAE/B,EAAI,IACxHA,GAAK,OAGC,GAAgB,MAAZuB,EACVJ,EAAOkB,EACPjB,EAAOkB,EACa,MAAhBV,GAAuC,MAAhBA,IAC1BT,GAAQkB,EAAYhB,EAAQA,EAAQV,OAAS,GAC7CS,GAAQkB,EAAYjB,EAAQA,EAAQV,OAAS,IAEzCa,IACJa,EAAYC,EAAY,GAEzBjB,EAAQX,KAAKS,EAAMC,EAAMlD,EAAGC,EAAIkE,GAAwB,EAAXN,EAAE/B,EAAI,GAAUsC,GAAwB,EAAXP,EAAE/B,EAAI,IAChFA,GAAK,OAGC,GAAgB,MAAZuB,EACVJ,EAAOkB,EA7EI,EAAI,GA6EKnE,EAAImE,GACxBjB,EAAOkB,EA9EI,EAAI,GA8EKnE,EAAImE,GACnBd,IACJa,EAAYC,EAAY,GAEzBD,GAAwB,EAAXN,EAAE/B,EAAI,GACnBsC,GAAwB,EAAXP,EAAE/B,EAAI,GACnBqB,EAAQX,KAAKS,EAAMC,EAAMiB,EApFd,EAAI,GAoFuBnE,EAAImE,GAAwBC,EApFvD,EAAI,GAoFgEnE,EAAImE,GAAwBD,EAAWC,GACtHtC,GAAK,OAGC,GAAgB,MAAZuB,EACVJ,EAAOkB,EAAYhB,EAAQA,EAAQV,OAAS,GAC5CS,EAAOkB,EAAYjB,EAAQA,EAAQV,OAAS,GAC5CU,EAAQX,KAAK2B,EAAYlB,EAAMmB,EAAYlB,EAAMlD,EA3FtC,EAAI,GA2FwCmE,EAAmB,IAAPlB,EAAcjD,GAAgBC,EA3FtF,EAAI,GA2FwFmE,EAAmB,IAAPlB,EAAcjD,GAAiBkE,EAAYnE,EAAKoE,EAAYnE,GAC/K6B,GAAK,OAGC,GAAgB,MAAZuB,EACVT,GAAKuB,EAAWC,EAAYD,EAAYnE,EAAIoE,GAC5CtC,GAAK,OAGC,GAAgB,MAAZuB,EAEVT,GAAKuB,EAAWC,EAAWD,EAAYC,EAAYpE,GAAKsD,EAAac,EAAYD,EAAY,IAC7FrC,GAAK,OAGC,GAAgB,MAAZuB,GAA+B,MAAZA,EACb,MAAZA,IACHrD,EAAIuD,EACJtD,EAAIuD,EACJL,EAAQwB,QAAS,IAEF,MAAZtB,GAAyC,GAAtBjE,EAAK+E,EAAYnE,IAAkC,GAAtBZ,EAAKgF,EAAYnE,MACpE2C,GAAKuB,EAAWC,EAAWpE,EAAGC,GACd,MAAZoD,IACHvB,GAAK,IAGPqC,EAAYnE,EACZoE,EAAYnE,OAGN,GAAgB,MAAZoD,EAAiB,IAC3BM,EAAQE,EAAE/B,EAAE,GACZ8B,EAAQC,EAAE/B,EAAE,GACZmB,EAAOY,EAAE/B,EAAE,GACXoB,EAAOW,EAAE/B,EAAE,GACXsB,EAAI,EACe,EAAfO,EAAMlB,SACLkB,EAAMlB,OAAS,GAClBS,EAAOD,EACPA,EAAOW,EACPR,MAEAF,EAAOU,EACPX,EAAOU,EAAMiB,OAAO,GACpBxB,GAAG,GAEJQ,EAAQD,EAAMkB,OAAO,GACrBlB,EAAQA,EAAMkB,OAAO,IAEtBpB,EAAUjE,aAAa2E,EAAWC,GAAYP,EAAE/B,EAAE,IAAK+B,EAAE/B,EAAE,IAAK+B,EAAE/B,EAAE,IAAK6B,GAAQC,GAAQN,EAAaa,EAAY,GAAU,EAALlB,GAASK,EAAac,EAAY,GAAU,EAALlB,GAC9JpB,GAAKsB,EACDK,MACEL,EAAI,EAAGA,EAAIK,EAAQhB,OAAQW,IAC/BD,EAAQX,KAAKiB,EAAQL,IAGvBe,EAAYhB,EAAQA,EAAQV,OAAO,GACnC2B,EAAYjB,EAAQA,EAAQV,OAAO,QAGnC+B,QAAQC,IAAIF,UAGdzC,EAAIqB,EAAQV,QACJ,GACPyB,EAAKY,MACLhD,EAAI,GACMqB,EAAQ,KAAOA,EAAQrB,EAAE,IAAMqB,EAAQ,KAAOA,EAAQrB,EAAE,KAClEqB,EAAQwB,QAAS,GAElBT,EAAKa,YAAcT,EAASxC,EACrBoC,ECnzBI,SAAXc,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKE,gBAAkBF,EAC/F,SAAZG,KACCH,EAAOD,MAENC,EAAKI,aAAa,MAAOC,EAAWC,QACpCC,EAAe,GAEfhB,QAAQiB,KAAK,0CAIN,SAATjH,EAASC,YAAoB,IAARA,GAAgBA,EAAQ,GAAK,GAAK,KAAO,IAmC5C,SAAlBiH,EAA4BlF,EAAIC,EAAIkF,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAW3B,EAAQ4B,OAiB7EzD,EAhBG0D,GAAO3F,EAAKmF,GAAM,EACrBS,GAAO3F,EAAKmF,GAAM,EAClBS,GAAOV,EAAKE,GAAM,EAClBS,GAAOV,EAAKE,GAAM,EAClBS,GAAOV,EAAKE,GAAM,EAClBS,GAAOV,EAAKE,GAAM,EAClBS,GAAQN,EAAME,GAAO,EACrBK,GAAQN,EAAME,GAAO,EACrBK,GAAQN,EAAME,GAAO,EACrBK,GAAQN,EAAME,GAAO,EACrBK,GAASJ,EAAOE,GAAQ,EACxBG,GAASJ,EAAOE,GAAQ,EACxBG,EAAKhB,EAAKvF,EACVwG,EAAKhB,EAAKvF,EACVwG,EAAKvI,KAAKW,KAAKsG,EAAKI,GAAMiB,GAAMpB,EAAKI,GAAMe,GAC3CG,EAAKxI,KAAKW,KAAKwG,EAAKE,GAAMiB,GAAMlB,EAAKE,GAAMe,UAEvCzC,IACJA,EAAS,CAAC,CAACtE,EAAGQ,EAAIP,EAAGQ,GAAK,CAACT,EAAG+F,EAAI9F,EAAG+F,IACrCE,EAAQ,GAET5B,EAAO6C,OAAOjB,GAAS5B,EAAO7B,OAAS,EAAG,EAAG,CAACzC,EAAG6G,EAAO5G,EAAG6G,IAC/Bb,GAAac,EAAKA,EAAKC,EAAKA,IAAnDC,EAAKC,IAAOD,EAAKC,KACrBzE,EAAS6B,EAAO7B,OAChBiD,EAAgBlF,EAAIC,EAAI0F,EAAKC,EAAKK,EAAMC,EAAMG,EAAOC,EAAOb,EAAW3B,EAAQ4B,GAC/ER,EAAgBmB,EAAOC,EAAOH,EAAMC,EAAML,EAAKC,EAAKT,EAAIC,EAAIC,EAAW3B,EAAQ4B,EAAQ,GAAK5B,EAAO7B,OAASA,KAEtG6B,MA3ELW,EAAMO,IAaT4B,EACU,oCACVC,EAAmB,qBA+DP/B,4BAQZgC,QAAA,iBAAQC,EAAMC,GACbA,EAASA,GAAU,OAQlBC,EAAGC,EAAIC,EAAI7F,EAAG8F,EAAKxE,EAAGyE,EAAOC,EAAWC,EANrCC,GADJT,EAAOA,GAAQ,WACGtD,MAAMgE,GACvBC,EAAU,EACV5D,EAAS,GACT6D,EAAS,GACTC,EAAYZ,EAAOY,WAAa,EAChCC,EAAQD,GAAa,UAEjBb,KAAOA,GACRF,EAAiBiB,KAAKf,KAAWA,EAAKgB,QAAQ,MAAQhB,EAAKgB,QAAQ,KAAO,KAC7EP,EAAStF,gBAAgB6E,GAAM,IAGtB,KADVE,EAAIO,EAAOvF,QAEVuF,EAAOQ,QAAQ,EAAG,GAClBR,EAAOxF,KAAK,EAAG,GACfiF,EAAI,OACE,IAAKA,EAAI,GAAK,OACd,yBAEY,IAAdO,EAAO,IAA+B,IAAlBA,EAAOP,EAAI,IAlFxB,SAAbgB,WAAcT,EAAQU,EAAQC,GACxBA,GAAuB,IAAZA,IACfA,EAAUjK,KAAKkK,KAAKZ,EAAOA,EAAOvF,OAAO,IAAKuF,EAAO,SAOrDlG,EALG+G,GAAmB,EAAbb,EAAO,GAChBc,GAAMH,EACNlB,EAAIO,EAAOvF,OACXI,EAAK,IAAMmF,EAAOP,EAAI,GAAKoB,GAC3B/F,GAAM4F,IAAYhK,KAAKW,IAAK2I,EAAOP,EAAI,GAAMO,EAAO,IAAM,KAASA,EAAOP,EAAI,GAAMO,EAAO,IAlB9E,SAAfe,aAAef,OAGblG,EAFG2F,EAAIO,EAAOvF,OACduG,EAPQ,SASJlH,EAAI,EAAGA,EAAI2F,EAAG3F,GAAK,GACtBkG,EAAOlG,GAAKkH,IAAQA,GAAOhB,EAAOlG,WAE7BkH,EAW4FD,CAAaf,GAAUc,GAAMd,EAAOP,EAAI,GAAKqB,OAG/IhG,EADGA,EACE,EAAIA,GAEHD,EAEFf,EAAI,EAAGA,EAAI2F,EAAG3F,GAAK,EACvBkG,EAAOlG,KAAOkG,EAAOlG,GAAK+G,GAAMhG,EAChCmF,EAAOlG,EAAI,KAAOkG,EAAOlG,EAAI,GAAKgH,GAAMhG,EAkExC2F,CAAWT,EAAQR,EAAOkB,OAAQlB,EAAOmB,cAErCxF,QAAU6E,EACVlG,EAAI,EAAGA,EAAI2F,EAAG3F,GAAK,EACvB4F,EAAK,CAAC1H,GAAIgI,EAAOlG,EAAI,GAAI7B,GAAI+H,EAAOlG,EAAI,IACxC6F,EAAK,CAAC3H,GAAIgI,EAAOlG,EAAI,GAAI7B,GAAI+H,EAAOlG,EAAI,IACxCwC,EAAO9B,KAAKkF,EAAIC,GAChBjC,EAAgBgC,EAAG1H,EAAG0H,EAAGzH,GAAI+H,EAAOlG,IAAKkG,EAAOlG,EAAI,IAAKkG,EAAOlG,EAAI,IAAKkG,EAAOlG,EAAI,GAAI6F,EAAG3H,EAAG2H,EAAG1H,EAAG,GAAiB,IAAZmI,GAAqB9D,EAAQA,EAAO7B,OAAS,OAEvJgF,EAAInD,EAAO7B,OACNX,EAAI,EAAGA,EAAI2F,EAAG3F,IAClB+F,EAAQvD,EAAOxC,GACfgG,EAAYxD,EAAOxC,EAAI,IAAM+F,GACxBA,EAAM7H,EAAI8H,EAAU9H,GAAM8H,EAAU7H,IAAM4H,EAAM5H,GAAK6H,EAAU9H,IAAM6H,EAAM7H,GAAM6H,IAAUC,IAAcD,EAAM7H,GAAK,GACxH8H,EAAU3G,GAAK0G,EAAM7H,EAAI8H,EAAU9H,EACnC8H,EAAU1G,GAAKyG,EAAM5H,EAAI6H,EAAU7H,EACnC6H,EAAU9D,EAAI6D,EACdC,EAAUmB,GAAKpB,EAAM7H,EACjBqI,GAAY,EAAJvG,GAAuF,EAA9EpD,KAAKW,IAAIyI,EAAU1G,GAAK0G,EAAU3G,GAAKmD,EAAOxC,EAAI,GAAGV,GAAKkD,EAAOxC,EAAI,GAAGX,MAC5FkH,EAAO,GAEJP,EAAU3G,GAAK+G,IACbJ,EAAU3G,GAQd+G,EAAUJ,EAAU3G,IAPpB2G,EAAU3G,GAAK,KACXW,IAAM2F,EAAI,IACbK,EAAU9H,GAAK,KACfkI,EAAUxJ,KAAKsK,IAAId,EAAS,MAC5BG,EAAO,OAOV/D,EAAO6C,OAAOrF,IAAK,GACnB2F,QAIFG,EAAM,GADNH,EAAK,EAAIS,EAAU,EAAK,GAGxBL,EAAQvD,EADRlB,EAAI,GAEAiF,EAAM,KACJvG,EAAI,EAAGA,EAAI2F,EAAG3F,IAClBiG,EAAIjG,EAAI8F,EACJC,EAAMoB,GAAKlB,IACdF,EAAQvD,IAASlB,IAElBsE,EAAKG,EAAM5H,GAAM8H,EAAIF,EAAM7H,GAAK6H,EAAM1G,GAAM0G,EAAMzG,GAClD+G,EAAOrG,GAAK,CAAC9B,EAAG+H,EAAG5G,GAAIyG,EAAK3H,EAAGyH,EAAItG,GAAI,EAAG6H,GAAI,GAC1CnH,IACHqG,EAAOrG,EAAI,GAAGV,GAAKsG,EAAKS,EAAOrG,EAAI,GAAG7B,GAGxCmD,EAAIkB,EAAOA,EAAO7B,OAAS,GAC3B0F,EAAOV,EAAI,GAAGrG,GAAKgC,EAAEnD,EAAIyH,EACzBS,EAAOV,EAAI,GAAGtG,GAAKiC,EAAEpD,EAAImI,EAAOA,EAAO1F,OAAS,GAAGzC,MAC7C,KACD8B,EAAI,EAAGA,EAAI2F,EAAG3F,IACd+F,EAAMoB,GAAKnH,EAAI8F,IAClBC,EAAQvD,IAASlB,IAElB+E,EAAOrG,GAAK+F,EAGTzE,EAAIkB,EAAO7B,OAAS,IACvB0F,EAAOrG,EAAE,GAAKwC,EAAOA,EAAO7B,OAAO,gBAKhCyG,KAAO,SAAAnB,OACPF,EAAQM,EAAQJ,EAAIN,EAAK,IAAMU,EAAOV,EAAI,UAC1CI,EAAMoB,GAAKlB,IACdF,EAAQA,EAAM7D,GAER6D,EAAM5H,GAAM8H,EAAIF,EAAM7H,GAAK6H,EAAM1G,GAAM0G,EAAMzG,UAGhD8H,KAAKC,OAASC,MAEdC,IAAMpE,GAAQA,EAAKI,aAAa+D,KAAKC,GAAID,KAAKF,MAE5CE,QAGRE,WAAA,oBAAW9B,UACHlC,WAAWgE,WAAWF,KAAM5B,eAG7BjC,OAAP,gBAAc8D,EAAI9B,EAAMC,UACf,IAAIlC,WAAW+D,EAAI9B,EAAMC,GAAS0B,iBAGpCK,SAAP,kBAAgBC,GACfvE,EAAOuE,EACPpE,gBAGMqE,IAAP,aAAWJ,UACHpE,EAAKyE,UAAUL,eAGhBC,WAAP,oBAAkBJ,EAAM1B,OAOtB3D,EAAG8F,EAAO7H,EAAG8F,EAAKiB,EAAIC,EAAIV,EAAWnC,EAAW2D,EAAOC,EALpDC,GADJtC,EAASA,GAAU,IACAsC,OAAS,IAC3BpB,EAASlB,EAAOkB,QAAU,IAC1B1I,EAAIwH,EAAOxH,GAAK,EAChBC,GAAKuH,EAAOvH,GAAK,GAAKyI,EACtBqB,EAAI9E,EAAK+E,MAAMC,QAAQzC,EAAOtD,MAAM,MAEjCsD,EAAO0C,SACVxB,GAAUA,EACVzI,EAAI,GAEgB,iBAAViJ,IACVA,EAAOjE,EAAKyE,UAAUR,IAEnBA,EAAKC,SACRD,EAAOA,EAAKC,QAETD,aAAgB5D,WACnBzB,EDg2BI,SAASsG,gBAAgBlI,IA5jCnB,SAAZmI,UAAY3L,SAA2B,iBAAXA,EA6jCxB2L,CAAUnI,EAAQ,MACrBA,EAAU,CAACA,QAIXoI,EAAIC,EAAGxI,EAAGqB,EAFPoH,EAAS,GACZ9C,EAAIxF,EAAQQ,WAER6H,EAAI,EAAGA,EAAI7C,EAAG6C,IAAK,KACvBnH,EAAUlB,EAAQqI,GAClBC,GAAU,IAAM/L,EAAO2E,EAAQ,IAAM,IAAM3E,EAAO2E,EAAQ,IAAM,KAChEkH,EAAKlH,EAAQV,OACRX,EAAI,EAAGA,EAAIuI,EAAIvI,IACnByI,GAAU/L,EAAO2E,EAAQrB,MAAQ,IAAMtD,EAAO2E,EAAQrB,MAAQ,IAAMtD,EAAO2E,EAAQrB,MAAQ,IAAMtD,EAAO2E,EAAQrB,MAAQ,IAAMtD,EAAO2E,EAAQrB,MAAQ,IAAMtD,EAAO2E,EAAQrB,IAAM,IAE7KqB,EAAQwB,SACX4F,GAAU,YAGLA,ECl3BDJ,CDiUA,SAASK,iBAAiBvI,EAAS4B,EAAG4G,EAAGC,EAAG/H,EAAGkG,EAAIC,WAExD3F,EAASsE,EAAG3F,EAAG9B,EAAGC,EADfmD,EAAInB,EAAQQ,QAEF,IAALW,OAERqE,GADAtE,EAAUlB,EAAQmB,IACNX,OACPX,EAAI,EAAGA,EAAI2F,EAAG3F,GAAK,EACvB9B,EAAImD,EAAQrB,GACZ7B,EAAIkD,EAAQrB,EAAE,GACdqB,EAAQrB,GAAK9B,EAAI6D,EAAI5D,EAAIyK,EAAI7B,EAC7B1F,EAAQrB,EAAE,GAAK9B,EAAIyK,EAAIxK,EAAI0C,EAAImG,SAGjC7G,EAAQ0I,OAAS,EACV1I,EC/UeuI,CAAiB,CAACtB,EAAK/F,SAAU2G,EAAO,EAAG,GAAIpB,EAAQ1I,EAAGC,QACxE,KACN4D,EAAI,CAAC7D,EAAGC,GAER2H,EAAM,GADNQ,EAAY1J,KAAKkK,IAAI,EAA6B,KAAzBpB,EAAOY,WAAa,KAG7CnC,EAAY,GADZmC,GAAa,GAEbwB,EAAQpL,EAAOwB,EAAI4H,EAAMkC,GAEzBH,IADAE,EAAQrL,EAAOyB,EAAIiJ,EAAKtB,IAAQc,IACfzI,IAAM2J,EAAQ5J,GAC1B8B,EAAI,EAAGA,EAAIsG,EAAWtG,IAC1B+G,EAAKrK,EAAOwB,EAAI8B,EAAI8F,EAAMkC,GAC1BhB,EAAKtK,EAAOyB,EAAIiJ,EAAKpH,EAAI8F,IAAQc,IAC7BhK,KAAKW,KAAKyJ,EAAKe,IAAUhB,EAAKe,GAASD,GAAS1D,GAAanE,IAAMsG,EAAY,KAClFvE,EAAErB,KAAKoH,EAAOC,GACdF,GAASb,EAAKe,IAAUhB,EAAKe,IAE9BA,EAAQf,EACRgB,EAAQf,EAETjF,EAAI,IAAMA,EAAE+G,KAAK,YAElBb,GAAKA,EAAEc,aAAa,IAAKhH,GAClBA,mCA9KIwF,EAAI9B,EAAMC,GACrBhC,GAAgBJ,SACXiE,GAAKA,EACSD,KAAK9B,QAAQC,EAAMC,GAgLxClC,EAAWwF,QAAU,SACrBxF,EAAWyF,UAAW,EAEtB/F,KAAcC,EAAKE,eAAeG"}