{"version": 3, "file": "DrawSVGPlugin.min.js", "sources": ["../src/DrawSVGPlugin.js"], "sourcesContent": ["/*!\n * DrawSVGPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\n\nlet gsap, _toArray, _doc, _win, _isEdge, _coreInitted, _warned, _getStyleSaver, _reverting,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_numExp = /[-+=\\.]*\\d+[\\.e\\-\\+]*\\d*[e\\-\\+]*\\d*/gi, //finds any numbers, including ones that start with += or -=, negative numbers, and ones in scientific notation like 1e-8.\n\t_types = {rect:[\"width\",\"height\"], circle:[\"r\",\"r\"], ellipse:[\"rx\",\"ry\"], line:[\"x2\",\"y2\"]},\n\t_round = value => Math.round(value * 10000) / 10000,\n\t_parseNum = value => parseFloat(value) || 0,\n\t_parseSingleVal = (value, length) => {\n\t\tlet num = _parseNum(value);\n\t\treturn ~value.indexOf(\"%\") ? num / 100 * length : num;\n\t},\n\t_getAttributeAsNumber = (target, attr) => _parseNum(target.getAttribute(attr)),\n\t_sqrt = Math.sqrt,\n\t_getDistance = (x1, y1, x2, y2, scaleX, scaleY) => _sqrt(((_parseNum(x2) - _parseNum(x1)) * scaleX) ** 2 + ((_parseNum(y2) - _parseNum(y1)) * scaleY) ** 2),\n\t_warn = message => console.warn(message),\n\t_hasNonScalingStroke = target => target.getAttribute(\"vector-effect\") === \"non-scaling-stroke\",\n\t_bonusValidated = 1, //<name>DrawSVGPlugin</name>\n\t//accepts values like \"100%\" or \"20% 80%\" or \"20 50\" and parses it into an absolute start and end position on the line/stroke based on its length. Returns an an array with the start and end values, like [0, 243]\n\t_parse = (value, length, defaultStart) => {\n\t\tlet i = value.indexOf(\" \"),\n\t\t\ts, e;\n\t\tif (i < 0) {\n\t\t\ts = defaultStart !== undefined ? defaultStart + \"\" : value;\n\t\t\te = value;\n\t\t} else {\n\t\t\ts = value.substr(0, i);\n\t\t\te = value.substr(i + 1);\n\t\t}\n\t\ts = _parseSingleVal(s, length);\n\t\te = _parseSingleVal(e, length);\n\t\treturn (s > e) ? [e, s] : [s, e];\n\t},\n\t_getLength = target => {\n\t\ttarget = _toArray(target)[0];\n\t\tif (!target) {\n\t\t\treturn 0;\n\t\t}\n\t\tlet type = target.tagName.toLowerCase(),\n\t\t\tstyle = target.style,\n\t\t\tscaleX = 1,\n\t\t\tscaleY = 1,\n\t\t\tlength, bbox, points, prevPoint, i, rx, ry;\n\t\tif (_hasNonScalingStroke(target)) { //non-scaling-stroke basically scales the shape and then strokes it at the screen-level (after transforms), thus we need to adjust the length accordingly.\n\t\t\tscaleY = target.getScreenCTM();\n\t\t\tscaleX = _sqrt(scaleY.a * scaleY.a + scaleY.b * scaleY.b);\n\t\t\tscaleY = _sqrt(scaleY.d * scaleY.d + scaleY.c * scaleY.c);\n\t\t}\n\t\ttry { //IE bug: calling <path>.getTotalLength() locks the repaint area of the stroke to whatever its current dimensions are on that frame/tick. To work around that, we must call getBBox() to force IE to recalculate things.\n\t\t\tbbox = target.getBBox(); //solely for fixing bug in IE - we don't actually use the bbox.\n\t\t} catch (e) {\n\t\t\t//firefox has a bug that throws an error if the element isn't visible.\n\t\t\t_warn(\"Some browsers won't measure invisible elements (like display:none or masks inside defs).\");\n\t\t}\n\t\tlet {x, y, width, height} = bbox || {x:0, y:0, width:0, height:0};\n\t\tif ((!bbox || (!width && !height)) && _types[type]) { //if the element isn't visible, try to discern width/height using its attributes.\n\t\t\twidth =_getAttributeAsNumber(target, _types[type][0]);\n\t\t\theight = _getAttributeAsNumber(target, _types[type][1]);\n\t\t\tif (type !== \"rect\" && type !== \"line\") { //double the radius for circles and ellipses\n\t\t\t\twidth *= 2;\n\t\t\t\theight *= 2;\n\t\t\t}\n\t\t\tif (type === \"line\") {\n\t\t\t\tx = _getAttributeAsNumber(target, \"x1\");\n\t\t\t\ty = _getAttributeAsNumber(target, \"y1\");\n\t\t\t\twidth = Math.abs(width - x);\n\t\t\t\theight = Math.abs(height - y);\n\t\t\t}\n\t\t}\n\t\tif (type === \"path\") {\n\t\t\tprevPoint = style.strokeDasharray;\n\t\t\tstyle.strokeDasharray = \"none\";\n\t\t\tlength = target.getTotalLength() || 0;\n\t\t\t_round(scaleX) !== _round(scaleY) && !_warned && (_warned = 1) && _warn(\"Warning: <path> length cannot be measured when vector-effect is non-scaling-stroke and the element isn't proportionally scaled.\");\n\t\t\tlength *= (scaleX + scaleY) / 2;\n\t\t\tstyle.strokeDasharray = prevPoint;\n\t\t} else if (type === \"rect\") {\n\t\t\tlength = width * 2 * scaleX + height * 2 * scaleY;\n\t\t} else if (type === \"line\") {\n\t\t\tlength = _getDistance(x, y, x + width, y + height, scaleX, scaleY);\n\t\t} else if (type === \"polyline\" || type === \"polygon\") {\n\t\t\tpoints = target.getAttribute(\"points\").match(_numExp) || [];\n\t\t\ttype === \"polygon\" && points.push(points[0], points[1]);\n\t\t\tlength = 0;\n\t\t\tfor (i = 2; i < points.length; i+=2) {\n\t\t\t\tlength += _getDistance(points[i-2], points[i-1], points[i], points[i+1], scaleX, scaleY) || 0;\n\t\t\t}\n\t\t} else if (type === \"circle\" || type === \"ellipse\") {\n\t\t\trx = (width / 2) * scaleX;\n\t\t\try = (height / 2) * scaleY;\n\t\t\tlength = Math.PI * ( 3 * (rx + ry) - _sqrt((3 * rx + ry) * (rx + 3 * ry)) );\n\t\t}\n\t\treturn length || 0;\n\t},\n\t_getPosition = (target, length) => {\n\t\ttarget = _toArray(target)[0];\n\t\tif (!target) {\n\t\t\treturn [0, 0];\n\t\t}\n\t\tlength || (length = _getLength(target) + 1);\n\t\tlet cs = _win.getComputedStyle(target),\n\t\t\tdash = cs.strokeDasharray || \"\",\n\t\t\toffset = _parseNum(cs.strokeDashoffset),\n\t\t\ti = dash.indexOf(\",\");\n\t\ti < 0 && (i = dash.indexOf(\" \"));\n\t\tdash = i < 0 ? length : _parseNum(dash.substr(0, i));\n\t\tdash > length && (dash = length);\n\t\treturn [-offset || 0, (dash - offset) || 0];\n\t},\n\t_initCore = () => {\n\t\tif (_windowExists()) {\n\t\t\t_doc = document;\n\t\t\t_win = window;\n\t\t\t_coreInitted = gsap = _getGSAP();\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_getStyleSaver = gsap.core.getStyleSaver;\n\t\t\t_reverting = gsap.core.reverting || function() {};\n\t\t\t_isEdge = (((_win.navigator || {}).userAgent || \"\").indexOf(\"Edge\") !== -1); //Microsoft Edge has a bug that causes it not to redraw the path correctly if the stroke-linecap is anything other than \"butt\" (like \"round\") and it doesn't match the stroke-linejoin. A way to trigger it is to change the stroke-miterlimit, so we'll only do that if/when we have to (to maximize performance)\n\t\t}\n\t};\n\n\nexport const DrawSVGPlugin = {\n\tversion:\"3.13.0\",\n\tname:\"drawSVG\",\n\tregister(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t},\n\tinit(target, value, tween, index, targets) {\n\t\tif (!target.getBBox) {\n\t\t\treturn false;\n\t\t}\n\t\t_coreInitted ||\t_initCore();\n\t\tlet length = _getLength(target),\n\t\t\tstart, end, cs;\n\t\tthis.styles = _getStyleSaver && _getStyleSaver(target, \"strokeDashoffset,strokeDasharray,strokeMiterlimit\");\n\t\tthis.tween = tween;\n\t\tthis._style = target.style;\n\t\tthis._target = target;\n\t\tif (value + \"\" === \"true\") {\n\t\t\tvalue = \"0 100%\";\n\t\t} else if (!value) {\n\t\t\tvalue = \"0 0\";\n\t\t} else if ((value + \"\").indexOf(\" \") === -1) {\n\t\t\tvalue = \"0 \" + value;\n\t\t}\n\t\tstart = _getPosition(target, length);\n\t\tend = _parse(value, length, start[0]);\n\t\tthis._length = _round(length);\n\t\tthis._dash = _round(start[1] - start[0]); //some browsers render artifacts if dash is 0, so we use a very small number in that case.\n\t\tthis._offset = _round(-start[0]);\n\t\tthis._dashPT = this.add(this, \"_dash\", this._dash, _round(end[1] - end[0]), 0, 0, 0, 0, 0, 1);\n\t\tthis._offsetPT = this.add(this, \"_offset\", this._offset, _round(-end[0]), 0, 0, 0, 0, 0, 1);\n\t\tif (_isEdge) { //to work around a bug in Microsoft Edge, animate the stroke-miterlimit by 0.0001 just to trigger the repaint (unnecessary if it's \"round\" and stroke-linejoin is also \"round\"). Imperceptible, relatively high-performance, and effective. Another option was to set the \"d\" <path> attribute to its current value on every tick, but that seems like it'd be much less performant.\n\t\t\tcs = _win.getComputedStyle(target);\n\t\t\tif (cs.strokeLinecap !== cs.strokeLinejoin) {\n\t\t\t\tend = _parseNum(cs.strokeMiterlimit);\n\t\t\t\tthis.add(target.style, \"strokeMiterlimit\", end, end + 0.01);\n\t\t\t}\n\t\t}\n\t\tthis._live = (_hasNonScalingStroke(target) || ~((value + \"\").indexOf(\"live\")));\n\t\tthis._nowrap = ~(value + \"\").indexOf(\"nowrap\");\n\t\tthis._props.push(\"drawSVG\");\n\t\treturn _bonusValidated;\n\t},\n\trender(ratio, data) {\n\t\tif (data.tween._time || !_reverting()) {\n\t\t\tlet pt = data._pt,\n\t\t\t\tstyle = data._style,\n\t\t\t\tlength, lengthRatio, dash, offset;\n\t\t\tif (pt) {\n\t\t\t\t//when the element has vector-effect=\"non-scaling-stroke\" and the SVG is resized (like on a window resize), it actually changes the length of the stroke! So we must sense that and make the proper adjustments.\n\t\t\t\tif (data._live) {\n\t\t\t\t\tlength = _getLength(data._target);\n\t\t\t\t\tif (length !== data._length) {\n\t\t\t\t\t\tlengthRatio = length / data._length;\n\t\t\t\t\t\tdata._length = length;\n\t\t\t\t\t\tif (data._offsetPT) {\n\t\t\t\t\t\t\tdata._offsetPT.s *= lengthRatio;\n\t\t\t\t\t\t\tdata._offsetPT.c *= lengthRatio;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (data._dashPT) {\n\t\t\t\t\t\t\tdata._dashPT.s *= lengthRatio;\n\t\t\t\t\t\t\tdata._dashPT.c *= lengthRatio;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tdata._dash *= lengthRatio;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\twhile (pt) {\n\t\t\t\t\tpt.r(ratio, pt.d);\n\t\t\t\t\tpt = pt._next;\n\t\t\t\t}\n\t\t\t\tdash = data._dash || ((ratio && ratio !== 1 && 0.0001) || 0); // only let it be zero if it's at the start or end of the tween.\n\t\t\t\tlength = data._length - dash + 0.1;\n\t\t\t\toffset = data._offset;\n\t\t\t\tdash && offset && dash + Math.abs(offset % data._length) > data._length - 0.05 && (offset += offset < 0 ? 0.005 : -0.005) && (length += 0.005);\n\t\t\t\tstyle.strokeDashoffset = dash ? offset : offset + 0.001;\n\t\t\t\tstyle.strokeDasharray = length < 0.1 ? \"none\" : dash ? dash + \"px,\" + (data._nowrap ? 999999 : length) + \"px\" : \"0px, 999999px\";\n\t\t\t}\n\t\t} else {\n\t\t\tdata.styles.revert();\n\t\t}\n\t},\n\tgetLength: _getLength,\n\tgetPosition: _getPosition\n};\n\n_getGSAP() && gsap.registerPlugin(DrawSVGPlugin);\n\nexport { DrawSVGPlugin as default };"], "names": ["_windowExists", "window", "_getGSAP", "gsap", "registerPlugin", "_round", "value", "Math", "round", "_parseNum", "parseFloat", "_parseSingleVal", "length", "num", "indexOf", "_getAttributeAsNumber", "target", "attr", "getAttribute", "_getDistance", "x1", "y1", "x2", "y2", "scaleX", "scaleY", "_sqrt", "_warn", "message", "console", "warn", "_hasNonScalingStroke", "_get<PERSON>ength", "_toArray", "bbox", "points", "prevPoint", "i", "rx", "ry", "type", "tagName", "toLowerCase", "style", "getScreenCTM", "a", "b", "d", "c", "getBBox", "e", "x", "y", "width", "height", "_types", "abs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getTotalLength", "_warned", "match", "_numExp", "push", "PI", "_getPosition", "cs", "_win", "getComputedStyle", "dash", "offset", "strokeDashoffset", "substr", "_initCore", "_coreInitted", "utils", "toArray", "_getStyleSaver", "core", "getStyleSaver", "_reverting", "reverting", "_isEdge", "navigator", "userAgent", "rect", "circle", "ellipse", "line", "sqrt", "DrawSVGPlugin", "version", "name", "register", "init", "tween", "start", "end", "styles", "_style", "_target", "_parse", "defaultStart", "s", "undefined", "_length", "_dash", "_offset", "_dashPT", "this", "add", "_offsetPT", "strokeLinecap", "strokeLinejoin", "strokeMiterlimit", "_live", "_nowrap", "_props", "render", "ratio", "data", "_time", "lengthRatio", "pt", "_pt", "r", "_next", "revert", "<PERSON><PERSON><PERSON><PERSON>", "getPosition"], "mappings": ";;;;;;;;;6MAYiB,SAAhBA,UAAyC,oBAAZC,OAClB,SAAXC,WAAiBC,GAASH,MAAoBG,EAAOF,OAAOE,OAASA,EAAKC,gBAAkBD,EAGnF,SAATE,EAASC,UAASC,KAAKC,MAAc,IAARF,GAAiB,IAClC,SAAZG,EAAYH,UAASI,WAAWJ,IAAU,EACxB,SAAlBK,EAAmBL,EAAOM,OACrBC,EAAMJ,EAAUH,UACZA,EAAMQ,QAAQ,KAAOD,EAAM,IAAMD,EAASC,EAE3B,SAAxBE,EAAyBC,EAAQC,UAASR,EAAUO,EAAOE,aAAaD,IAEzD,SAAfE,EAAgBC,EAAIC,EAAIC,EAAIC,EAAIC,EAAQC,UAAWC,EAAMnB,UAAEE,EAAUa,GAAMb,EAAUW,IAAOI,EAAW,aAAMf,EAAUc,GAAMd,EAAUY,IAAOI,EAAW,IACjJ,SAARE,EAAQC,UAAWC,QAAQC,KAAKF,GACT,SAAvBG,EAAuBf,SAAmD,uBAAzCA,EAAOE,aAAa,iBAiBxC,SAAbc,EAAahB,QACZA,EAASiB,EAASjB,GAAQ,WAElB,MAMPJ,EAAQsB,EAAMC,EAAQC,EAAWC,EAAGC,EAAIC,EAJrCC,EAAOxB,EAAOyB,QAAQC,cACzBC,EAAQ3B,EAAO2B,MACfnB,EAAS,EACTC,EAAS,EAENM,EAAqBf,KACxBS,EAAST,EAAO4B,eAChBpB,EAASE,EAAMD,EAAOoB,EAAIpB,EAAOoB,EAAIpB,EAAOqB,EAAIrB,EAAOqB,GACvDrB,EAASC,EAAMD,EAAOsB,EAAItB,EAAOsB,EAAItB,EAAOuB,EAAIvB,EAAOuB,QAGvDd,EAAOlB,EAAOiC,UACb,MAAOC,GAERvB,EAAM,kGAEqBO,GAAQ,CAACiB,EAAE,EAAGC,EAAE,EAAGC,MAAM,EAAGC,OAAO,GAA1DH,IAAAA,EAAGC,IAAAA,EAAGC,IAAAA,MAAOC,IAAAA,UACZpB,IAAUmB,GAAUC,KAAYC,EAAOf,KAC5Ca,EAAOtC,EAAsBC,EAAQuC,EAAOf,GAAM,IAClDc,EAASvC,EAAsBC,EAAQuC,EAAOf,GAAM,IACvC,SAATA,GAA4B,SAATA,IACtBa,GAAS,EACTC,GAAU,GAEE,SAATd,IACHW,EAAIpC,EAAsBC,EAAQ,MAClCoC,EAAIrC,EAAsBC,EAAQ,MAClCqC,EAAQ9C,KAAKiD,IAAIH,EAAQF,GACzBG,EAAS/C,KAAKiD,IAAIF,EAASF,KAGhB,SAATZ,EACHJ,EAAYO,EAAMc,gBAClBd,EAAMc,gBAAkB,OACxB7C,EAASI,EAAO0C,kBAAoB,EACpCrD,EAAOmB,KAAYnB,EAAOoB,KAAYkC,IAAYA,EAAU,IAAMhC,EAAM,mIACxEf,IAAWY,EAASC,GAAU,EAC9BkB,EAAMc,gBAAkBrB,OAClB,GAAa,SAATI,EACV5B,EAAiB,EAARyC,EAAY7B,EAAkB,EAAT8B,EAAa7B,OACrC,GAAa,SAATe,EACV5B,EAASO,EAAagC,EAAGC,EAAGD,EAAIE,EAAOD,EAAIE,EAAQ9B,EAAQC,QACrD,GAAa,aAATe,GAAgC,YAATA,MACjCL,EAASnB,EAAOE,aAAa,UAAU0C,MAAMC,IAAY,GAChD,YAATrB,GAAsBL,EAAO2B,KAAK3B,EAAO,GAAIA,EAAO,IACpDvB,EAAS,EACJyB,EAAI,EAAGA,EAAIF,EAAOvB,OAAQyB,GAAG,EACjCzB,GAAUO,EAAagB,EAAOE,EAAE,GAAIF,EAAOE,EAAE,GAAIF,EAAOE,GAAIF,EAAOE,EAAE,GAAIb,EAAQC,IAAW,MAE1E,WAATe,GAA8B,YAATA,IAC/BF,EAAMe,EAAQ,EAAK7B,EACnBe,EAAMe,EAAS,EAAK7B,EACpBb,EAASL,KAAKwD,IAAO,GAAKzB,EAAKC,GAAMb,GAAO,EAAIY,EAAKC,IAAOD,EAAK,EAAIC,aAE/D3B,GAAU,EAEH,SAAfoD,EAAgBhD,EAAQJ,QACvBI,EAASiB,EAASjB,GAAQ,UAElB,CAAC,EAAG,GAEDJ,EAAXA,GAAoBoB,EAAWhB,GAAU,MACrCiD,EAAKC,EAAKC,iBAAiBnD,GAC9BoD,EAAOH,EAAGR,iBAAmB,GAC7BY,EAAS5D,EAAUwD,EAAGK,kBACtBjC,EAAI+B,EAAKtD,QAAQ,YAClBuB,EAAI,IAAMA,EAAI+B,EAAKtD,QAAQ,MAEpBF,GADPwD,EAAO/B,EAAI,EAAIzB,EAASH,EAAU2D,EAAKG,OAAO,EAAGlC,OAC/B+B,EAAOxD,GAClB,EAAEyD,GAAU,EAAID,EAAOC,GAAW,GAE9B,SAAZG,IACKxE,MAEHkE,EAAOjE,OACPwE,EAAetE,EAAOD,IACtB+B,EAAW9B,EAAKuE,MAAMC,QACtBC,EAAiBzE,EAAK0E,KAAKC,cAC3BC,EAAa5E,EAAK0E,KAAKG,WAAa,aACpCC,GAAyE,MAA5Df,EAAKgB,WAAa,IAAIC,WAAa,IAAIrE,QAAQ,aApH3DX,EAAM8B,EAAgBiC,EAAMe,EAASR,EAAcd,EAASiB,EAAgBG,EAG/ElB,EAAU,wCACVN,EAAS,CAAC6B,KAAK,CAAC,QAAQ,UAAWC,OAAO,CAAC,IAAI,KAAMC,QAAQ,CAAC,KAAK,MAAOC,KAAK,CAAC,KAAK,OAQrF7D,EAAQnB,KAAKiF,KA6GDC,EAAgB,CAC5BC,QAAQ,SACRC,KAAK,UACLC,2BAASf,GACR1E,EAAO0E,EACPL,KAEDqB,mBAAK7E,EAAQV,EAAOwF,OACd9E,EAAOiC,eACJ,EAERwB,GAAgBD,QAEfuB,EAAOC,EAAK/B,EADTrD,EAASoB,EAAWhB,eAEnBiF,OAASrB,GAAkBA,EAAe5D,EAAQ,0DAClD8E,MAAQA,OACRI,OAASlF,EAAO2B,WAChBwD,QAAUnF,EACXV,EAAQ,IAAO,OAClBA,EAAQ,SACGA,GAE8B,KAA9BA,EAAQ,IAAIQ,QAAQ,OAC/BR,EAAQ,KAAOA,GAFfA,EAAQ,MAKT0F,EAjIQ,SAATI,OAAU9F,EAAOM,EAAQyF,OAEvBC,EAAGpD,EADAb,EAAI/B,EAAMQ,QAAQ,YAIrBoC,EAFGb,EAAI,GACPiE,OAAqBC,IAAjBF,EAA6BA,EAAe,GAAK/F,EACjDA,IAEJgG,EAAIhG,EAAMiE,OAAO,EAAGlC,GAChB/B,EAAMiE,OAAOlC,EAAI,IAEtBiE,EAAI3F,EAAgB2F,EAAG1F,IACvBsC,EAAIvC,EAAgBuC,EAAGtC,IACf0F,EAAS,CAACpD,EAAGoD,GAAK,CAACA,EAAGpD,GAqHxBkD,CAAO9F,EAAOM,GADpBmF,EAAQ/B,EAAahD,EAAQJ,IACK,SAC7B4F,QAAUnG,EAAOO,QACjB6F,MAAQpG,EAAO0F,EAAM,GAAKA,EAAM,SAChCW,QAAUrG,GAAQ0F,EAAM,SACxBY,QAAUC,KAAKC,IAAID,KAAM,QAASA,KAAKH,MAAOpG,EAAO2F,EAAI,GAAKA,EAAI,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,QACtFc,UAAYF,KAAKC,IAAID,KAAM,UAAWA,KAAKF,QAASrG,GAAQ2F,EAAI,IAAK,EAAG,EAAG,EAAG,EAAG,EAAG,GACrFf,IACHhB,EAAKC,EAAKC,iBAAiBnD,IACpB+F,gBAAkB9C,EAAG+C,iBAC3BhB,EAAMvF,EAAUwD,EAAGgD,uBACdJ,IAAI7F,EAAO2B,MAAO,mBAAoBqD,EAAKA,EAAM,WAGnDkB,MAASnF,EAAqBf,MAAcV,EAAQ,IAAIQ,QAAQ,aAChEqG,UAAY7G,EAAQ,IAAIQ,QAAQ,eAChCsG,OAAOtD,KAAK,WAlJA,GAqJlBuD,uBAAOC,EAAOC,MACTA,EAAKzB,MAAM0B,QAAUzC,IAAc,KAGrCnE,EAAQ6G,EAAarD,EAAMC,EAFxBqD,EAAKH,EAAKI,IACbhF,EAAQ4E,EAAKrB,UAEVwB,EAAI,KAEHH,EAAKL,QACRtG,EAASoB,EAAWuF,EAAKpB,YACVoB,EAAKf,UACnBiB,EAAc7G,EAAS2G,EAAKf,QAC5Be,EAAKf,QAAU5F,EACX2G,EAAKT,YACRS,EAAKT,UAAUR,GAAKmB,EACpBF,EAAKT,UAAU9D,GAAKyE,GAEjBF,EAAKZ,SACRY,EAAKZ,QAAQL,GAAKmB,EAClBF,EAAKZ,QAAQ3D,GAAKyE,GAElBF,EAAKd,OAASgB,GAIVC,GACNA,EAAGE,EAAEN,EAAOI,EAAG3E,GACf2E,EAAKA,EAAGG,MAETzD,EAAOmD,EAAKd,OAAWa,GAAmB,IAAVA,GAAe,MAAW,EAC1D1G,EAAS2G,EAAKf,QAAUpC,EAAO,GAC/BC,EAASkD,EAAKb,QACdtC,GAAQC,GAAUD,EAAO7D,KAAKiD,IAAIa,EAASkD,EAAKf,SAAWe,EAAKf,QAAU,MAASnC,GAAUA,EAAS,EAAI,MAAS,QAAWzD,GAAU,MACxI+B,EAAM2B,iBAAmBF,EAAOC,EAASA,EAAS,KAClD1B,EAAMc,gBAAkB7C,EAAS,GAAM,OAASwD,EAAOA,EAAO,OAASmD,EAAKJ,QAAU,OAASvG,GAAU,KAAO,sBAGjH2G,EAAKtB,OAAO6B,UAGdC,UAAW/F,EACXgG,YAAahE,GAGd9D,KAAcC,EAAKC,eAAeqF"}