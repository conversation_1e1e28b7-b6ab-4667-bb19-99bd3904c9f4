{"version": 3, "sources": ["../../gsap/utils/matrix.js", "../../gsap/Flip.js"], "sourcesContent": ["/*!\n * matrix 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nvar _doc,\n    _win,\n    _doc<PERSON><PERSON>,\n    _body,\n    _div<PERSON><PERSON>r,\n    _svg<PERSON><PERSON>r,\n    _identityMatrix,\n    _gEl,\n    _transformProp = \"transform\",\n    _transformOriginProp = _transformProp + \"Origin\",\n    _hasOffsetBug,\n    _setDoc = function _setDoc(element) {\n  var doc = element.ownerDocument || element;\n\n  if (!(_transformProp in element.style) && \"msTransform\" in element.style) {\n    //to improve compatibility with old Microsoft browsers\n    _transformProp = \"msTransform\";\n    _transformOriginProp = _transformProp + \"Origin\";\n  }\n\n  while (doc.parentNode && (doc = doc.parentNode)) {}\n\n  _win = window;\n  _identityMatrix = new Matrix2D();\n\n  if (doc) {\n    _doc = doc;\n    _docElement = doc.documentElement;\n    _body = doc.body;\n    _gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\"); // prevent any existing CSS from transforming it\n\n    _gEl.style.transform = \"none\"; // now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon.\n\n    var d1 = doc.createElement(\"div\"),\n        d2 = doc.createElement(\"div\"),\n        root = doc && (doc.body || doc.firstElementChild);\n\n    if (root && root.appendChild) {\n      root.appendChild(d1);\n      d1.appendChild(d2);\n      d1.setAttribute(\"style\", \"position:static;transform:translate3d(0,0,1px)\");\n      _hasOffsetBug = d2.offsetParent !== d1;\n      root.removeChild(d1);\n    }\n  }\n\n  return doc;\n},\n    _forceNonZeroScale = function _forceNonZeroScale(e) {\n  // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n  var a, cache;\n\n  while (e && e !== _body) {\n    cache = e._gsap;\n    cache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\n    if (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n      cache.scaleX = cache.scaleY = 1e-4;\n      cache.renderTransform(1, cache);\n      a ? a.push(cache) : a = [cache];\n    }\n\n    e = e.parentNode;\n  }\n\n  return a;\n},\n    // possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n// _forceDisplay = e => {\n// \tlet a = [],\n// \t\tparent;\n// \twhile (e && e !== _body) {\n// \t\tparent = e.parentNode;\n// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n// \t\tparent || _body.appendChild(e);\n// \t\te = parent;\n// \t}\n// \treturn a;\n// },\n// _revertDisplay = a => {\n// \tfor (let i = 0; i < a.length; i+=3) {\n// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n// \t}\n// },\n_svgTemps = [],\n    //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n_divTemps = [],\n    _getDocScrollTop = function _getDocScrollTop() {\n  return _win.pageYOffset || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0;\n},\n    _getDocScrollLeft = function _getDocScrollLeft() {\n  return _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0;\n},\n    _svgOwner = function _svgOwner(element) {\n  return element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null);\n},\n    _isFixed = function _isFixed(element) {\n  if (_win.getComputedStyle(element).position === \"fixed\") {\n    return true;\n  }\n\n  element = element.parentNode;\n\n  if (element && element.nodeType === 1) {\n    // avoid document fragments which will throw an error.\n    return _isFixed(element);\n  }\n},\n    _createSibling = function _createSibling(element, i) {\n  if (element.parentNode && (_doc || _setDoc(element))) {\n    var svg = _svgOwner(element),\n        ns = svg ? svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\" : \"http://www.w3.org/1999/xhtml\",\n        type = svg ? i ? \"rect\" : \"g\" : \"div\",\n        x = i !== 2 ? 0 : 100,\n        y = i === 3 ? 100 : 0,\n        css = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n        e = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n\n    if (i) {\n      if (!svg) {\n        if (!_divContainer) {\n          _divContainer = _createSibling(element);\n          _divContainer.style.cssText = css;\n        }\n\n        e.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n\n        _divContainer.appendChild(e);\n      } else {\n        _svgContainer || (_svgContainer = _createSibling(element));\n        e.setAttribute(\"width\", 0.01);\n        e.setAttribute(\"height\", 0.01);\n        e.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n\n        _svgContainer.appendChild(e);\n      }\n    }\n\n    return e;\n  }\n\n  throw \"Need document and parent.\";\n},\n    _consolidate = function _consolidate(m) {\n  // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n  var c = new Matrix2D(),\n      i = 0;\n\n  for (; i < m.numberOfItems; i++) {\n    c.multiply(m.getItem(i).matrix);\n  }\n\n  return c;\n},\n    _getCTM = function _getCTM(svg) {\n  var m = svg.getCTM(),\n      transform;\n\n  if (!m) {\n    // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n    transform = svg.style[_transformProp];\n    svg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\n    svg.appendChild(_gEl);\n    m = _gEl.getCTM();\n    svg.removeChild(_gEl);\n    transform ? svg.style[_transformProp] = transform : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n  }\n\n  return m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n},\n    _placeSiblings = function _placeSiblings(element, adjustGOffset) {\n  var svg = _svgOwner(element),\n      isRootSVG = element === svg,\n      siblings = svg ? _svgTemps : _divTemps,\n      parent = element.parentNode,\n      appendToEl = parent && !svg && parent.shadowRoot && parent.shadowRoot.appendChild ? parent.shadowRoot : parent,\n      container,\n      m,\n      b,\n      x,\n      y,\n      cs;\n\n  if (element === _win) {\n    return element;\n  }\n\n  siblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n  container = svg ? _svgContainer : _divContainer;\n\n  if (svg) {\n    if (isRootSVG) {\n      b = _getCTM(element);\n      x = -b.e / b.a;\n      y = -b.f / b.d;\n      m = _identityMatrix;\n    } else if (element.getBBox) {\n      b = element.getBBox();\n      m = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\n      m = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\n      x = m.a * b.x + m.c * b.y;\n      y = m.b * b.x + m.d * b.y;\n    } else {\n      // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n      m = new Matrix2D();\n      x = y = 0;\n    }\n\n    if (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n      x = y = 0;\n    }\n\n    (isRootSVG ? svg : parent).appendChild(container);\n    container.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n  } else {\n    x = y = 0;\n\n    if (_hasOffsetBug) {\n      // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n      m = element.offsetParent;\n      b = element;\n\n      while (b && (b = b.parentNode) && b !== m && b.parentNode) {\n        if ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n          x = b.offsetLeft;\n          y = b.offsetTop;\n          b = 0;\n        }\n      }\n    }\n\n    cs = _win.getComputedStyle(element);\n\n    if (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n      m = element.offsetParent;\n\n      while (parent && parent !== m) {\n        // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n        x += parent.scrollLeft || 0;\n        y += parent.scrollTop || 0;\n        parent = parent.parentNode;\n      }\n    }\n\n    b = container.style;\n    b.top = element.offsetTop - y + \"px\";\n    b.left = element.offsetLeft - x + \"px\";\n    b[_transformProp] = cs[_transformProp];\n    b[_transformOriginProp] = cs[_transformOriginProp]; // b.border = m.border;\n    // b.borderLeftStyle = m.borderLeftStyle;\n    // b.borderTopStyle = m.borderTopStyle;\n    // b.borderLeftWidth = m.borderLeftWidth;\n    // b.borderTopWidth = m.borderTopWidth;\n\n    b.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n    appendToEl.appendChild(container);\n  }\n\n  return container;\n},\n    _setMatrix = function _setMatrix(m, a, b, c, d, e, f) {\n  m.a = a;\n  m.b = b;\n  m.c = c;\n  m.d = d;\n  m.e = e;\n  m.f = f;\n  return m;\n};\n\nexport var Matrix2D = /*#__PURE__*/function () {\n  function Matrix2D(a, b, c, d, e, f) {\n    if (a === void 0) {\n      a = 1;\n    }\n\n    if (b === void 0) {\n      b = 0;\n    }\n\n    if (c === void 0) {\n      c = 0;\n    }\n\n    if (d === void 0) {\n      d = 1;\n    }\n\n    if (e === void 0) {\n      e = 0;\n    }\n\n    if (f === void 0) {\n      f = 0;\n    }\n\n    _setMatrix(this, a, b, c, d, e, f);\n  }\n\n  var _proto = Matrix2D.prototype;\n\n  _proto.inverse = function inverse() {\n    var a = this.a,\n        b = this.b,\n        c = this.c,\n        d = this.d,\n        e = this.e,\n        f = this.f,\n        determinant = a * d - b * c || 1e-10;\n    return _setMatrix(this, d / determinant, -b / determinant, -c / determinant, a / determinant, (c * f - d * e) / determinant, -(a * f - b * e) / determinant);\n  };\n\n  _proto.multiply = function multiply(matrix) {\n    var a = this.a,\n        b = this.b,\n        c = this.c,\n        d = this.d,\n        e = this.e,\n        f = this.f,\n        a2 = matrix.a,\n        b2 = matrix.c,\n        c2 = matrix.b,\n        d2 = matrix.d,\n        e2 = matrix.e,\n        f2 = matrix.f;\n    return _setMatrix(this, a2 * a + c2 * c, a2 * b + c2 * d, b2 * a + d2 * c, b2 * b + d2 * d, e + e2 * a + f2 * c, f + e2 * b + f2 * d);\n  };\n\n  _proto.clone = function clone() {\n    return new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n  };\n\n  _proto.equals = function equals(matrix) {\n    var a = this.a,\n        b = this.b,\n        c = this.c,\n        d = this.d,\n        e = this.e,\n        f = this.f;\n    return a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f;\n  };\n\n  _proto.apply = function apply(point, decoratee) {\n    if (decoratee === void 0) {\n      decoratee = {};\n    }\n\n    var x = point.x,\n        y = point.y,\n        a = this.a,\n        b = this.b,\n        c = this.c,\n        d = this.d,\n        e = this.e,\n        f = this.f;\n    decoratee.x = x * a + y * c + e || 0;\n    decoratee.y = x * b + y * d + f || 0;\n    return decoratee;\n  };\n\n  return Matrix2D;\n}(); // Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\n\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) {\n  // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n  if (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n    return new Matrix2D();\n  }\n\n  var zeroScales = _forceNonZeroScale(element),\n      svg = _svgOwner(element),\n      temps = svg ? _svgTemps : _divTemps,\n      container = _placeSiblings(element, adjustGOffset),\n      b1 = temps[0].getBoundingClientRect(),\n      b2 = temps[1].getBoundingClientRect(),\n      b3 = temps[2].getBoundingClientRect(),\n      parent = container.parentNode,\n      isFixed = !includeScrollInFixed && _isFixed(element),\n      m = new Matrix2D((b2.left - b1.left) / 100, (b2.top - b1.top) / 100, (b3.left - b1.left) / 100, (b3.top - b1.top) / 100, b1.left + (isFixed ? 0 : _getDocScrollLeft()), b1.top + (isFixed ? 0 : _getDocScrollTop()));\n\n  parent.removeChild(container);\n\n  if (zeroScales) {\n    b1 = zeroScales.length;\n\n    while (b1--) {\n      b2 = zeroScales[b1];\n      b2.scaleX = b2.scaleY = 0;\n      b2.renderTransform(1, b2);\n    }\n  }\n\n  return inverse ? m.inverse() : m;\n}\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM }; // export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }", "/*!\n * Flip 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n\n/* eslint-disable */\nimport { getGlobalMatrix, _getDocScrollTop, _getDocScrollLeft, Matrix2D, _setDoc, _getCTM } from \"./utils/matrix.js\";\n\nvar _id = 1,\n    _toArray,\n    gsap,\n    _batch,\n    _batchAction,\n    _body,\n    _closestTenth,\n    _getStyleSaver,\n    _forEachBatch = function _forEachBatch(batch, name) {\n  return batch.actions.forEach(function (a) {\n    return a.vars[name] && a.vars[name](a);\n  });\n},\n    _batchLookup = {},\n    _RAD2DEG = 180 / Math.PI,\n    _DEG2RAD = Math.PI / 180,\n    _emptyObj = {},\n    _dashedNameLookup = {},\n    _memoizedRemoveProps = {},\n    _listToArray = function _listToArray(list) {\n  return typeof list === \"string\" ? list.split(\" \").join(\"\").split(\",\") : list;\n},\n    // removes extra spaces contaminating the names, returns an Array.\n_callbacks = _listToArray(\"onStart,onUpdate,onComplete,onReverseComplete,onInterrupt\"),\n    _removeProps = _listToArray(\"transform,transformOrigin,width,height,position,top,left,opacity,zIndex,maxWidth,maxHeight,minWidth,minHeight\"),\n    _getEl = function _getEl(target) {\n  return _toArray(target)[0] || console.warn(\"Element not found:\", target);\n},\n    _round = function _round(value) {\n  return Math.round(value * 10000) / 10000 || 0;\n},\n    _toggleClass = function _toggleClass(targets, className, action) {\n  return targets.forEach(function (el) {\n    return el.classList[action](className);\n  });\n},\n    _reserved = {\n  zIndex: 1,\n  kill: 1,\n  simple: 1,\n  spin: 1,\n  clearProps: 1,\n  targets: 1,\n  toggleClass: 1,\n  onComplete: 1,\n  onUpdate: 1,\n  onInterrupt: 1,\n  onStart: 1,\n  delay: 1,\n  repeat: 1,\n  repeatDelay: 1,\n  yoyo: 1,\n  scale: 1,\n  fade: 1,\n  absolute: 1,\n  props: 1,\n  onEnter: 1,\n  onLeave: 1,\n  custom: 1,\n  paused: 1,\n  nested: 1,\n  prune: 1,\n  absoluteOnLeave: 1\n},\n    _fitReserved = {\n  zIndex: 1,\n  simple: 1,\n  clearProps: 1,\n  scale: 1,\n  absolute: 1,\n  fitChild: 1,\n  getVars: 1,\n  props: 1\n},\n    _camelToDashed = function _camelToDashed(p) {\n  return p.replace(/([A-Z])/g, \"-$1\").toLowerCase();\n},\n    _copy = function _copy(obj, exclude) {\n  var result = {},\n      p;\n\n  for (p in obj) {\n    exclude[p] || (result[p] = obj[p]);\n  }\n\n  return result;\n},\n    _memoizedProps = {},\n    _memoizeProps = function _memoizeProps(props) {\n  var p = _memoizedProps[props] = _listToArray(props);\n\n  _memoizedRemoveProps[props] = p.concat(_removeProps);\n  return p;\n},\n    _getInverseGlobalMatrix = function _getInverseGlobalMatrix(el) {\n  // integrates caching for improved performance\n  var cache = el._gsap || gsap.core.getCache(el);\n\n  if (cache.gmCache === gsap.ticker.frame) {\n    return cache.gMatrix;\n  }\n\n  cache.gmCache = gsap.ticker.frame;\n  return cache.gMatrix = getGlobalMatrix(el, true, false, true);\n},\n    _getDOMDepth = function _getDOMDepth(el, invert, level) {\n  if (level === void 0) {\n    level = 0;\n  }\n\n  // In invert is true, the sibling depth is increments of 1, and parent/nesting depth is increments of 1000. This lets us order elements in an Array to reflect document flow.\n  var parent = el.parentNode,\n      inc = 1000 * Math.pow(10, level) * (invert ? -1 : 1),\n      l = invert ? -inc * 900 : 0;\n\n  while (el) {\n    l += inc;\n    el = el.previousSibling;\n  }\n\n  return parent ? l + _getDOMDepth(parent, invert, level + 1) : l;\n},\n    _orderByDOMDepth = function _orderByDOMDepth(comps, invert, isElStates) {\n  comps.forEach(function (comp) {\n    return comp.d = _getDOMDepth(isElStates ? comp.element : comp.t, invert);\n  });\n  comps.sort(function (c1, c2) {\n    return c1.d - c2.d;\n  });\n  return comps;\n},\n    _recordInlineStyles = function _recordInlineStyles(elState, props) {\n  // records the current inline CSS properties into an Array in alternating name/value pairs that's stored in a \"css\" property on the state object so that we can revert later.\n  var style = elState.element.style,\n      a = elState.css = elState.css || [],\n      i = props.length,\n      p,\n      v;\n\n  while (i--) {\n    p = props[i];\n    v = style[p] || style.getPropertyValue(p);\n    a.push(v ? p : _dashedNameLookup[p] || (_dashedNameLookup[p] = _camelToDashed(p)), v);\n  }\n\n  return style;\n},\n    _applyInlineStyles = function _applyInlineStyles(state) {\n  var css = state.css,\n      style = state.element.style,\n      i = 0;\n  state.cache.uncache = 1;\n\n  for (; i < css.length; i += 2) {\n    css[i + 1] ? style[css[i]] = css[i + 1] : style.removeProperty(css[i]);\n  }\n\n  if (!css[css.indexOf(\"transform\") + 1] && style.translate) {\n    // CSSPlugin adds scale, translate, and rotate inline CSS as \"none\" in order to keep CSS rules from contaminating transforms.\n    style.removeProperty(\"translate\");\n    style.removeProperty(\"scale\");\n    style.removeProperty(\"rotate\");\n  }\n},\n    _setFinalStates = function _setFinalStates(comps, onlyTransforms) {\n  comps.forEach(function (c) {\n    return c.a.cache.uncache = 1;\n  });\n  onlyTransforms || comps.finalStates.forEach(_applyInlineStyles);\n},\n    _absoluteProps = \"paddingTop,paddingRight,paddingBottom,paddingLeft,gridArea,transition\".split(\",\"),\n    // properties that we must record just\n_makeAbsolute = function _makeAbsolute(elState, fallbackNode, ignoreBatch) {\n  var element = elState.element,\n      width = elState.width,\n      height = elState.height,\n      uncache = elState.uncache,\n      getProp = elState.getProp,\n      style = element.style,\n      i = 4,\n      result,\n      displayIsNone,\n      cs;\n  typeof fallbackNode !== \"object\" && (fallbackNode = elState);\n\n  if (_batch && ignoreBatch !== 1) {\n    _batch._abs.push({\n      t: element,\n      b: elState,\n      a: elState,\n      sd: 0\n    });\n\n    _batch._final.push(function () {\n      return (elState.cache.uncache = 1) && _applyInlineStyles(elState);\n    });\n\n    return element;\n  }\n\n  displayIsNone = getProp(\"display\") === \"none\";\n\n  if (!elState.isVisible || displayIsNone) {\n    displayIsNone && (_recordInlineStyles(elState, [\"display\"]).display = fallbackNode.display);\n    elState.matrix = fallbackNode.matrix;\n    elState.width = width = elState.width || fallbackNode.width;\n    elState.height = height = elState.height || fallbackNode.height;\n  }\n\n  _recordInlineStyles(elState, _absoluteProps);\n\n  cs = window.getComputedStyle(element);\n\n  while (i--) {\n    style[_absoluteProps[i]] = cs[_absoluteProps[i]]; // record paddings as px-based because if removed from grid, percentage-based ones could be altered.\n  }\n\n  style.gridArea = \"1 / 1 / 1 / 1\";\n  style.transition = \"none\";\n  style.position = \"absolute\";\n  style.width = width + \"px\";\n  style.height = height + \"px\";\n  style.top || (style.top = \"0px\");\n  style.left || (style.left = \"0px\");\n\n  if (uncache) {\n    result = new ElementState(element);\n  } else {\n    // better performance\n    result = _copy(elState, _emptyObj);\n    result.position = \"absolute\";\n\n    if (elState.simple) {\n      var bounds = element.getBoundingClientRect();\n      result.matrix = new Matrix2D(1, 0, 0, 1, bounds.left + _getDocScrollLeft(), bounds.top + _getDocScrollTop());\n    } else {\n      result.matrix = getGlobalMatrix(element, false, false, true);\n    }\n  }\n\n  result = _fit(result, elState, true);\n  elState.x = _closestTenth(result.x, 0.01);\n  elState.y = _closestTenth(result.y, 0.01);\n  return element;\n},\n    _filterComps = function _filterComps(comps, targets) {\n  if (targets !== true) {\n    targets = _toArray(targets);\n    comps = comps.filter(function (c) {\n      if (targets.indexOf((c.sd < 0 ? c.b : c.a).element) !== -1) {\n        return true;\n      } else {\n        c.t._gsap.renderTransform(1); // we must force transforms to render on anything that isn't being made position: absolute, otherwise the absolute position happens and then when animation begins it applies transforms which can create a new stacking context, throwing off positioning!\n\n\n        if (c.b.isVisible) {\n          c.t.style.width = c.b.width + \"px\"; // otherwise things can collapse when contents are made position: absolute.\n\n          c.t.style.height = c.b.height + \"px\";\n        }\n      }\n    });\n  }\n\n  return comps;\n},\n    _makeCompsAbsolute = function _makeCompsAbsolute(comps) {\n  return _orderByDOMDepth(comps, true).forEach(function (c) {\n    return (c.a.isVisible || c.b.isVisible) && _makeAbsolute(c.sd < 0 ? c.b : c.a, c.b, 1);\n  });\n},\n    _findElStateInState = function _findElStateInState(state, other) {\n  return other && state.idLookup[_parseElementState(other).id] || state.elementStates[0];\n},\n    _parseElementState = function _parseElementState(elOrNode, props, simple, other) {\n  return elOrNode instanceof ElementState ? elOrNode : elOrNode instanceof FlipState ? _findElStateInState(elOrNode, other) : new ElementState(typeof elOrNode === \"string\" ? _getEl(elOrNode) || console.warn(elOrNode + \" not found\") : elOrNode, props, simple);\n},\n    _recordProps = function _recordProps(elState, props) {\n  var getProp = gsap.getProperty(elState.element, null, \"native\"),\n      obj = elState.props = {},\n      i = props.length;\n\n  while (i--) {\n    obj[props[i]] = (getProp(props[i]) + \"\").trim();\n  }\n\n  obj.zIndex && (obj.zIndex = parseFloat(obj.zIndex) || 0);\n  return elState;\n},\n    _applyProps = function _applyProps(element, props) {\n  var style = element.style || element,\n      // could pass in a vars object.\n  p;\n\n  for (p in props) {\n    style[p] = props[p];\n  }\n},\n    _getID = function _getID(el) {\n  var id = el.getAttribute(\"data-flip-id\");\n  id || el.setAttribute(\"data-flip-id\", id = \"auto-\" + _id++);\n  return id;\n},\n    _elementsFromElementStates = function _elementsFromElementStates(elStates) {\n  return elStates.map(function (elState) {\n    return elState.element;\n  });\n},\n    _handleCallback = function _handleCallback(callback, elStates, tl) {\n  return callback && elStates.length && tl.add(callback(_elementsFromElementStates(elStates), tl, new FlipState(elStates, 0, true)), 0);\n},\n    _fit = function _fit(fromState, toState, scale, applyProps, fitChild, vars) {\n  var element = fromState.element,\n      cache = fromState.cache,\n      parent = fromState.parent,\n      x = fromState.x,\n      y = fromState.y,\n      width = toState.width,\n      height = toState.height,\n      scaleX = toState.scaleX,\n      scaleY = toState.scaleY,\n      rotation = toState.rotation,\n      bounds = toState.bounds,\n      styles = vars && _getStyleSaver && _getStyleSaver(element, \"transform,width,height\"),\n      dimensionState = fromState,\n      _toState$matrix = toState.matrix,\n      e = _toState$matrix.e,\n      f = _toState$matrix.f,\n      deep = fromState.bounds.width !== bounds.width || fromState.bounds.height !== bounds.height || fromState.scaleX !== scaleX || fromState.scaleY !== scaleY || fromState.rotation !== rotation,\n      simple = !deep && fromState.simple && toState.simple && !fitChild,\n      skewX,\n      fromPoint,\n      toPoint,\n      getProp,\n      parentMatrix,\n      matrix,\n      bbox;\n\n  if (simple || !parent) {\n    scaleX = scaleY = 1;\n    rotation = skewX = 0;\n  } else {\n    parentMatrix = _getInverseGlobalMatrix(parent);\n    matrix = parentMatrix.clone().multiply(toState.ctm ? toState.matrix.clone().multiply(toState.ctm) : toState.matrix); // root SVG elements have a ctm that we must factor out (for example, viewBox:\"0 0 94 94\" with a width of 200px would scale the internals by 2.127 but when we're matching the size of the root <svg> element itself, that scaling shouldn't factor in!)\n\n    rotation = _round(Math.atan2(matrix.b, matrix.a) * _RAD2DEG);\n    skewX = _round(Math.atan2(matrix.c, matrix.d) * _RAD2DEG + rotation) % 360; // in very rare cases, minor rounding might end up with 360 which should be 0.\n\n    scaleX = Math.sqrt(Math.pow(matrix.a, 2) + Math.pow(matrix.b, 2));\n    scaleY = Math.sqrt(Math.pow(matrix.c, 2) + Math.pow(matrix.d, 2)) * Math.cos(skewX * _DEG2RAD);\n\n    if (fitChild) {\n      fitChild = _toArray(fitChild)[0];\n      getProp = gsap.getProperty(fitChild);\n      bbox = fitChild.getBBox && typeof fitChild.getBBox === \"function\" && fitChild.getBBox();\n      dimensionState = {\n        scaleX: getProp(\"scaleX\"),\n        scaleY: getProp(\"scaleY\"),\n        width: bbox ? bbox.width : Math.ceil(parseFloat(getProp(\"width\", \"px\"))),\n        height: bbox ? bbox.height : parseFloat(getProp(\"height\", \"px\"))\n      };\n    }\n\n    cache.rotation = rotation + \"deg\";\n    cache.skewX = skewX + \"deg\";\n  }\n\n  if (scale) {\n    scaleX *= width === dimensionState.width || !dimensionState.width ? 1 : width / dimensionState.width; // note if widths are both 0, we should make scaleX 1 - some elements have box-sizing that incorporates padding, etc. and we don't want it to collapse in that case.\n\n    scaleY *= height === dimensionState.height || !dimensionState.height ? 1 : height / dimensionState.height;\n    cache.scaleX = scaleX;\n    cache.scaleY = scaleY;\n  } else {\n    width = _closestTenth(width * scaleX / dimensionState.scaleX, 0);\n    height = _closestTenth(height * scaleY / dimensionState.scaleY, 0);\n    element.style.width = width + \"px\";\n    element.style.height = height + \"px\";\n  } // if (fromState.isFixed) { // commented out because it's now taken care of in getGlobalMatrix() with a flag at the end.\n  // \te -= _getDocScrollLeft();\n  // \tf -= _getDocScrollTop();\n  // }\n\n\n  applyProps && _applyProps(element, toState.props);\n\n  if (simple || !parent) {\n    x += e - fromState.matrix.e;\n    y += f - fromState.matrix.f;\n  } else if (deep || parent !== toState.parent) {\n    cache.renderTransform(1, cache);\n    matrix = getGlobalMatrix(fitChild || element, false, false, true);\n    fromPoint = parentMatrix.apply({\n      x: matrix.e,\n      y: matrix.f\n    });\n    toPoint = parentMatrix.apply({\n      x: e,\n      y: f\n    });\n    x += toPoint.x - fromPoint.x;\n    y += toPoint.y - fromPoint.y;\n  } else {\n    // use a faster/cheaper algorithm if we're just moving x/y\n    parentMatrix.e = parentMatrix.f = 0;\n    toPoint = parentMatrix.apply({\n      x: e - fromState.matrix.e,\n      y: f - fromState.matrix.f\n    });\n    x += toPoint.x;\n    y += toPoint.y;\n  }\n\n  x = _closestTenth(x, 0.02);\n  y = _closestTenth(y, 0.02);\n\n  if (vars && !(vars instanceof ElementState)) {\n    // revert\n    styles && styles.revert();\n  } else {\n    // or apply the transform immediately\n    cache.x = x + \"px\";\n    cache.y = y + \"px\";\n    cache.renderTransform(1, cache);\n  }\n\n  if (vars) {\n    vars.x = x;\n    vars.y = y;\n    vars.rotation = rotation;\n    vars.skewX = skewX;\n\n    if (scale) {\n      vars.scaleX = scaleX;\n      vars.scaleY = scaleY;\n    } else {\n      vars.width = width;\n      vars.height = height;\n    }\n  }\n\n  return vars || cache;\n},\n    _parseState = function _parseState(targetsOrState, vars) {\n  return targetsOrState instanceof FlipState ? targetsOrState : new FlipState(targetsOrState, vars);\n},\n    _getChangingElState = function _getChangingElState(toState, fromState, id) {\n  var to1 = toState.idLookup[id],\n      to2 = toState.alt[id];\n  return to2.isVisible && (!(fromState.getElementState(to2.element) || to2).isVisible || !to1.isVisible) ? to2 : to1;\n},\n    _bodyMetrics = [],\n    _bodyProps = \"width,height,overflowX,overflowY\".split(\",\"),\n    _bodyLocked,\n    _lockBodyScroll = function _lockBodyScroll(lock) {\n  // if there's no scrollbar, we should lock that so that measurements don't get affected by temporary repositioning, like if something is centered in the window.\n  if (lock !== _bodyLocked) {\n    var s = _body.style,\n        w = _body.clientWidth === window.outerWidth,\n        h = _body.clientHeight === window.outerHeight,\n        i = 4;\n\n    if (lock && (w || h)) {\n      while (i--) {\n        _bodyMetrics[i] = s[_bodyProps[i]];\n      }\n\n      if (w) {\n        s.width = _body.clientWidth + \"px\";\n        s.overflowY = \"hidden\";\n      }\n\n      if (h) {\n        s.height = _body.clientHeight + \"px\";\n        s.overflowX = \"hidden\";\n      }\n\n      _bodyLocked = lock;\n    } else if (_bodyLocked) {\n      while (i--) {\n        _bodyMetrics[i] ? s[_bodyProps[i]] = _bodyMetrics[i] : s.removeProperty(_camelToDashed(_bodyProps[i]));\n      }\n\n      _bodyLocked = lock;\n    }\n  }\n},\n    _fromTo = function _fromTo(fromState, toState, vars, relative) {\n  // relative is -1 if \"from()\", and 1 if \"to()\"\n  fromState instanceof FlipState && toState instanceof FlipState || console.warn(\"Not a valid state object.\");\n  vars = vars || {};\n\n  var _vars = vars,\n      clearProps = _vars.clearProps,\n      onEnter = _vars.onEnter,\n      onLeave = _vars.onLeave,\n      absolute = _vars.absolute,\n      absoluteOnLeave = _vars.absoluteOnLeave,\n      custom = _vars.custom,\n      delay = _vars.delay,\n      paused = _vars.paused,\n      repeat = _vars.repeat,\n      repeatDelay = _vars.repeatDelay,\n      yoyo = _vars.yoyo,\n      toggleClass = _vars.toggleClass,\n      nested = _vars.nested,\n      _zIndex = _vars.zIndex,\n      scale = _vars.scale,\n      fade = _vars.fade,\n      stagger = _vars.stagger,\n      spin = _vars.spin,\n      prune = _vars.prune,\n      props = (\"props\" in vars ? vars : fromState).props,\n      tweenVars = _copy(vars, _reserved),\n      animation = gsap.timeline({\n    delay: delay,\n    paused: paused,\n    repeat: repeat,\n    repeatDelay: repeatDelay,\n    yoyo: yoyo,\n    data: \"isFlip\"\n  }),\n      remainingProps = tweenVars,\n      entering = [],\n      leaving = [],\n      comps = [],\n      swapOutTargets = [],\n      spinNum = spin === true ? 1 : spin || 0,\n      spinFunc = typeof spin === \"function\" ? spin : function () {\n    return spinNum;\n  },\n      interrupted = fromState.interrupted || toState.interrupted,\n      addFunc = animation[relative !== 1 ? \"to\" : \"from\"],\n      v,\n      p,\n      endTime,\n      i,\n      el,\n      comp,\n      state,\n      targets,\n      finalStates,\n      fromNode,\n      toNode,\n      run,\n      a,\n      b; //relative || (toState = (new FlipState(toState.targets, {props: props})).fit(toState, scale));\n\n\n  for (p in toState.idLookup) {\n    toNode = !toState.alt[p] ? toState.idLookup[p] : _getChangingElState(toState, fromState, p);\n    el = toNode.element;\n    fromNode = fromState.idLookup[p];\n    fromState.alt[p] && el === fromNode.element && (fromState.alt[p].isVisible || !toNode.isVisible) && (fromNode = fromState.alt[p]);\n\n    if (fromNode) {\n      comp = {\n        t: el,\n        b: fromNode,\n        a: toNode,\n        sd: fromNode.element === el ? 0 : toNode.isVisible ? 1 : -1\n      };\n      comps.push(comp);\n\n      if (comp.sd) {\n        if (comp.sd < 0) {\n          comp.b = toNode;\n          comp.a = fromNode;\n        } // for swapping elements that got interrupted, we must re-record the inline styles to ensure they're not tainted. Remember, .batch() permits getState() not to force in-progress flips to their end state.\n\n\n        interrupted && _recordInlineStyles(comp.b, props ? _memoizedRemoveProps[props] : _removeProps);\n        fade && comps.push(comp.swap = {\n          t: fromNode.element,\n          b: comp.b,\n          a: comp.a,\n          sd: -comp.sd,\n          swap: comp\n        });\n      }\n\n      el._flip = fromNode.element._flip = _batch ? _batch.timeline : animation;\n    } else if (toNode.isVisible) {\n      comps.push({\n        t: el,\n        b: _copy(toNode, {\n          isVisible: 1\n        }),\n        a: toNode,\n        sd: 0,\n        entering: 1\n      }); // to include it in the \"entering\" Array and do absolute positioning if necessary\n\n      el._flip = _batch ? _batch.timeline : animation;\n    }\n  }\n\n  props && (_memoizedProps[props] || _memoizeProps(props)).forEach(function (p) {\n    return tweenVars[p] = function (i) {\n      return comps[i].a.props[p];\n    };\n  });\n  comps.finalStates = finalStates = [];\n\n  run = function run() {\n    _orderByDOMDepth(comps);\n\n    _lockBodyScroll(true); // otherwise, measurements may get thrown off when things get fit.\n    // TODO: cache the matrix, especially for parent because it'll probably get reused quite a bit, but lock it to a particular cycle(?).\n\n\n    for (i = 0; i < comps.length; i++) {\n      comp = comps[i];\n      a = comp.a;\n      b = comp.b;\n\n      if (prune && !a.isDifferent(b) && !comp.entering) {\n        // only flip if things changed! Don't omit it from comps initially because that'd prevent the element from being positioned absolutely (if necessary)\n        comps.splice(i--, 1);\n      } else {\n        el = comp.t;\n        nested && !(comp.sd < 0) && i && (a.matrix = getGlobalMatrix(el, false, false, true)); // moving a parent affects the position of children\n\n        if (b.isVisible && a.isVisible) {\n          if (comp.sd < 0) {\n            // swapping OUT (swap direction of -1 is out)\n            state = new ElementState(el, props, fromState.simple);\n\n            _fit(state, a, scale, 0, 0, state);\n\n            state.matrix = getGlobalMatrix(el, false, false, true);\n            state.css = comp.b.css;\n            comp.a = a = state;\n            fade && (el.style.opacity = interrupted ? b.opacity : a.opacity);\n            stagger && swapOutTargets.push(el);\n          } else if (comp.sd > 0 && fade) {\n            // swapping IN (swap direction of 1 is in)\n            el.style.opacity = interrupted ? a.opacity - b.opacity : \"0\";\n          }\n\n          _fit(a, b, scale, props);\n        } else if (b.isVisible !== a.isVisible) {\n          // either entering or leaving (one side is invisible)\n          if (!b.isVisible) {\n            // entering\n            a.isVisible && entering.push(a);\n            comps.splice(i--, 1);\n          } else if (!a.isVisible) {\n            // leaving\n            b.css = a.css;\n            leaving.push(b);\n            comps.splice(i--, 1);\n            absolute && nested && _fit(a, b, scale, props);\n          }\n        }\n\n        if (!scale) {\n          el.style.maxWidth = Math.max(a.width, b.width) + \"px\";\n          el.style.maxHeight = Math.max(a.height, b.height) + \"px\";\n          el.style.minWidth = Math.min(a.width, b.width) + \"px\";\n          el.style.minHeight = Math.min(a.height, b.height) + \"px\";\n        }\n\n        nested && toggleClass && el.classList.add(toggleClass);\n      }\n\n      finalStates.push(a);\n    }\n\n    var classTargets;\n\n    if (toggleClass) {\n      classTargets = finalStates.map(function (s) {\n        return s.element;\n      });\n      nested && classTargets.forEach(function (e) {\n        return e.classList.remove(toggleClass);\n      }); // there could be a delay, so don't leave the classes applied (we'll do it in a timeline callback)\n    }\n\n    _lockBodyScroll(false);\n\n    if (scale) {\n      tweenVars.scaleX = function (i) {\n        return comps[i].a.scaleX;\n      };\n\n      tweenVars.scaleY = function (i) {\n        return comps[i].a.scaleY;\n      };\n    } else {\n      tweenVars.width = function (i) {\n        return comps[i].a.width + \"px\";\n      };\n\n      tweenVars.height = function (i) {\n        return comps[i].a.height + \"px\";\n      };\n\n      tweenVars.autoRound = vars.autoRound || false;\n    }\n\n    tweenVars.x = function (i) {\n      return comps[i].a.x + \"px\";\n    };\n\n    tweenVars.y = function (i) {\n      return comps[i].a.y + \"px\";\n    };\n\n    tweenVars.rotation = function (i) {\n      return comps[i].a.rotation + (spin ? spinFunc(i, targets[i], targets) * 360 : 0);\n    };\n\n    tweenVars.skewX = function (i) {\n      return comps[i].a.skewX;\n    };\n\n    targets = comps.map(function (c) {\n      return c.t;\n    });\n\n    if (_zIndex || _zIndex === 0) {\n      tweenVars.modifiers = {\n        zIndex: function zIndex() {\n          return _zIndex;\n        }\n      };\n      tweenVars.zIndex = _zIndex;\n      tweenVars.immediateRender = vars.immediateRender !== false;\n    }\n\n    fade && (tweenVars.opacity = function (i) {\n      return comps[i].sd < 0 ? 0 : comps[i].sd > 0 ? comps[i].a.opacity : \"+=0\";\n    });\n\n    if (swapOutTargets.length) {\n      stagger = gsap.utils.distribute(stagger);\n      var dummyArray = targets.slice(swapOutTargets.length);\n\n      tweenVars.stagger = function (i, el) {\n        return stagger(~swapOutTargets.indexOf(el) ? targets.indexOf(comps[i].swap.t) : i, el, dummyArray);\n      };\n    } // // for testing...\n    // gsap.delayedCall(vars.data ? 50 : 1, function() {\n    // \tanimation.eventCallback(\"onComplete\", () => _setFinalStates(comps, !clearProps));\n    // \taddFunc.call(animation, targets, tweenVars, 0).play();\n    // });\n    // return;\n\n\n    _callbacks.forEach(function (name) {\n      return vars[name] && animation.eventCallback(name, vars[name], vars[name + \"Params\"]);\n    }); // apply callbacks to the timeline, not tweens (because \"custom\" timing can make multiple tweens)\n\n\n    if (custom && targets.length) {\n      // bust out the custom properties as their own tweens so they can use different eases, durations, etc.\n      remainingProps = _copy(tweenVars, _reserved);\n\n      if (\"scale\" in custom) {\n        custom.scaleX = custom.scaleY = custom.scale;\n        delete custom.scale;\n      }\n\n      for (p in custom) {\n        v = _copy(custom[p], _fitReserved);\n        v[p] = tweenVars[p];\n        !(\"duration\" in v) && \"duration\" in tweenVars && (v.duration = tweenVars.duration);\n        v.stagger = tweenVars.stagger;\n        addFunc.call(animation, targets, v, 0);\n        delete remainingProps[p];\n      }\n    }\n\n    if (targets.length || leaving.length || entering.length) {\n      toggleClass && animation.add(function () {\n        return _toggleClass(classTargets, toggleClass, animation._zTime < 0 ? \"remove\" : \"add\");\n      }, 0) && !paused && _toggleClass(classTargets, toggleClass, \"add\");\n      targets.length && addFunc.call(animation, targets, remainingProps, 0);\n    }\n\n    _handleCallback(onEnter, entering, animation);\n\n    _handleCallback(onLeave, leaving, animation);\n\n    var batchTl = _batch && _batch.timeline;\n\n    if (batchTl) {\n      batchTl.add(animation, 0);\n\n      _batch._final.push(function () {\n        return _setFinalStates(comps, !clearProps);\n      });\n    }\n\n    endTime = animation.duration();\n    animation.call(function () {\n      var forward = animation.time() >= endTime;\n      forward && !batchTl && _setFinalStates(comps, !clearProps);\n      toggleClass && _toggleClass(classTargets, toggleClass, forward ? \"remove\" : \"add\");\n    });\n  };\n\n  absoluteOnLeave && (absolute = comps.filter(function (comp) {\n    return !comp.sd && !comp.a.isVisible && comp.b.isVisible;\n  }).map(function (comp) {\n    return comp.a.element;\n  }));\n\n  if (_batch) {\n    var _batch$_abs;\n\n    absolute && (_batch$_abs = _batch._abs).push.apply(_batch$_abs, _filterComps(comps, absolute));\n\n    _batch._run.push(run);\n  } else {\n    absolute && _makeCompsAbsolute(_filterComps(comps, absolute)); // when making absolute, we must go in a very particular order so that document flow changes don't affect things. Don't make it visible if both the before and after states are invisible! There's no point, and it could make things appear visible during the flip that shouldn't be.\n\n    run();\n  }\n\n  var anim = _batch ? _batch.timeline : animation;\n\n  anim.revert = function () {\n    return _killFlip(anim, 1, 1);\n  }; // a Flip timeline should behave very different when reverting - it should actually jump to the end so that styles get cleared out.\n\n\n  return anim;\n},\n    _interrupt = function _interrupt(tl) {\n  tl.vars.onInterrupt && tl.vars.onInterrupt.apply(tl, tl.vars.onInterruptParams || []);\n  tl.getChildren(true, false, true).forEach(_interrupt);\n},\n    _killFlip = function _killFlip(tl, action, force) {\n  // action: 0 = nothing, 1 = complete, 2 = only kill (don't complete)\n  if (tl && tl.progress() < 1 && (!tl.paused() || force)) {\n    if (action) {\n      _interrupt(tl);\n\n      action < 2 && tl.progress(1); // we should also kill it in case it was added to a parent timeline.\n\n      tl.kill();\n    }\n\n    return true;\n  }\n},\n    _createLookup = function _createLookup(state) {\n  var lookup = state.idLookup = {},\n      alt = state.alt = {},\n      elStates = state.elementStates,\n      i = elStates.length,\n      elState;\n\n  while (i--) {\n    elState = elStates[i];\n    lookup[elState.id] ? alt[elState.id] = elState : lookup[elState.id] = elState;\n  }\n};\n\nvar FlipState = /*#__PURE__*/function () {\n  function FlipState(targets, vars, targetsAreElementStates) {\n    this.props = vars && vars.props;\n    this.simple = !!(vars && vars.simple);\n\n    if (targetsAreElementStates) {\n      this.targets = _elementsFromElementStates(targets);\n      this.elementStates = targets;\n\n      _createLookup(this);\n    } else {\n      this.targets = _toArray(targets);\n      var soft = vars && (vars.kill === false || vars.batch && !vars.kill);\n      _batch && !soft && _batch._kill.push(this);\n      this.update(soft || !!_batch); // when batching, don't force in-progress flips to their end; we need to do that AFTER all getStates() are called.\n    }\n  }\n\n  var _proto = FlipState.prototype;\n\n  _proto.update = function update(soft) {\n    var _this = this;\n\n    this.elementStates = this.targets.map(function (el) {\n      return new ElementState(el, _this.props, _this.simple);\n    });\n\n    _createLookup(this);\n\n    this.interrupt(soft);\n    this.recordInlineStyles();\n    return this;\n  };\n\n  _proto.clear = function clear() {\n    this.targets.length = this.elementStates.length = 0;\n\n    _createLookup(this);\n\n    return this;\n  };\n\n  _proto.fit = function fit(state, scale, nested) {\n    var elStatesInOrder = _orderByDOMDepth(this.elementStates.slice(0), false, true),\n        toElStates = (state || this).idLookup,\n        i = 0,\n        fromNode,\n        toNode;\n\n    for (; i < elStatesInOrder.length; i++) {\n      fromNode = elStatesInOrder[i];\n      nested && (fromNode.matrix = getGlobalMatrix(fromNode.element, false, false, true)); // moving a parent affects the position of children\n\n      toNode = toElStates[fromNode.id];\n      toNode && _fit(fromNode, toNode, scale, true, 0, fromNode);\n      fromNode.matrix = getGlobalMatrix(fromNode.element, false, false, true);\n    }\n\n    return this;\n  };\n\n  _proto.getProperty = function getProperty(element, property) {\n    var es = this.getElementState(element) || _emptyObj;\n\n    return (property in es ? es : es.props || _emptyObj)[property];\n  };\n\n  _proto.add = function add(state) {\n    var i = state.targets.length,\n        lookup = this.idLookup,\n        alt = this.alt,\n        index,\n        es,\n        es2;\n\n    while (i--) {\n      es = state.elementStates[i];\n      es2 = lookup[es.id];\n\n      if (es2 && (es.element === es2.element || alt[es.id] && alt[es.id].element === es.element)) {\n        // if the flip id is already in this FlipState, replace it!\n        index = this.elementStates.indexOf(es.element === es2.element ? es2 : alt[es.id]);\n        this.targets.splice(index, 1, state.targets[i]);\n        this.elementStates.splice(index, 1, es);\n      } else {\n        this.targets.push(state.targets[i]);\n        this.elementStates.push(es);\n      }\n    }\n\n    state.interrupted && (this.interrupted = true);\n    state.simple || (this.simple = false);\n\n    _createLookup(this);\n\n    return this;\n  };\n\n  _proto.compare = function compare(state) {\n    var l1 = state.idLookup,\n        l2 = this.idLookup,\n        unchanged = [],\n        changed = [],\n        enter = [],\n        leave = [],\n        targets = [],\n        a1 = state.alt,\n        a2 = this.alt,\n        place = function place(s1, s2, el) {\n      return (s1.isVisible !== s2.isVisible ? s1.isVisible ? enter : leave : s1.isVisible ? changed : unchanged).push(el) && targets.push(el);\n    },\n        placeIfDoesNotExist = function placeIfDoesNotExist(s1, s2, el) {\n      return targets.indexOf(el) < 0 && place(s1, s2, el);\n    },\n        s1,\n        s2,\n        p,\n        el,\n        s1Alt,\n        s2Alt,\n        c1,\n        c2;\n\n    for (p in l1) {\n      s1Alt = a1[p];\n      s2Alt = a2[p];\n      s1 = !s1Alt ? l1[p] : _getChangingElState(state, this, p);\n      el = s1.element;\n      s2 = l2[p];\n\n      if (s2Alt) {\n        c2 = s2.isVisible || !s2Alt.isVisible && el === s2.element ? s2 : s2Alt;\n        c1 = s1Alt && !s1.isVisible && !s1Alt.isVisible && c2.element === s1Alt.element ? s1Alt : s1; //c1.element !== c2.element && c1.element === s2.element && (c2 = s2);\n\n        if (c1.isVisible && c2.isVisible && c1.element !== c2.element) {\n          // swapping, so force into \"changed\" array\n          (c1.isDifferent(c2) ? changed : unchanged).push(c1.element, c2.element);\n          targets.push(c1.element, c2.element);\n        } else {\n          place(c1, c2, c1.element);\n        }\n\n        s1Alt && c1.element === s1Alt.element && (s1Alt = l1[p]);\n        placeIfDoesNotExist(c1.element !== s2.element && s1Alt ? s1Alt : c1, s2, s2.element);\n        placeIfDoesNotExist(s1Alt && s1Alt.element === s2Alt.element ? s1Alt : c1, s2Alt, s2Alt.element);\n        s1Alt && placeIfDoesNotExist(s1Alt, s2Alt.element === s1Alt.element ? s2Alt : s2, s1Alt.element);\n      } else {\n        !s2 ? enter.push(el) : !s2.isDifferent(s1) ? unchanged.push(el) : place(s1, s2, el);\n        s1Alt && placeIfDoesNotExist(s1Alt, s2, s1Alt.element);\n      }\n    }\n\n    for (p in l2) {\n      if (!l1[p]) {\n        leave.push(l2[p].element);\n        a2[p] && leave.push(a2[p].element);\n      }\n    }\n\n    return {\n      changed: changed,\n      unchanged: unchanged,\n      enter: enter,\n      leave: leave\n    };\n  };\n\n  _proto.recordInlineStyles = function recordInlineStyles() {\n    var props = _memoizedRemoveProps[this.props] || _removeProps,\n        i = this.elementStates.length;\n\n    while (i--) {\n      _recordInlineStyles(this.elementStates[i], props);\n    }\n  };\n\n  _proto.interrupt = function interrupt(soft) {\n    var _this2 = this;\n\n    // soft = DON'T force in-progress flip animations to completion (like when running a batch, we can't immediately kill flips when getting states because it could contaminate positioning and other .getState() calls that will run in the batch (we kill AFTER all the .getState() calls complete).\n    var timelines = [];\n    this.targets.forEach(function (t) {\n      var tl = t._flip,\n          foundInProgress = _killFlip(tl, soft ? 0 : 1);\n\n      soft && foundInProgress && timelines.indexOf(tl) < 0 && tl.add(function () {\n        return _this2.updateVisibility();\n      });\n      foundInProgress && timelines.push(tl);\n    });\n    !soft && timelines.length && this.updateVisibility(); // if we found an in-progress Flip animation, we must record all the values in their current state at that point BUT we should update the isVisible value AFTER pushing that flip to completion so that elements that are entering or leaving will populate those Arrays properly.\n\n    this.interrupted || (this.interrupted = !!timelines.length);\n  };\n\n  _proto.updateVisibility = function updateVisibility() {\n    this.elementStates.forEach(function (es) {\n      var b = es.element.getBoundingClientRect();\n      es.isVisible = !!(b.width || b.height || b.top || b.left);\n      es.uncache = 1;\n    });\n  };\n\n  _proto.getElementState = function getElementState(element) {\n    return this.elementStates[this.targets.indexOf(_getEl(element))];\n  };\n\n  _proto.makeAbsolute = function makeAbsolute() {\n    return _orderByDOMDepth(this.elementStates.slice(0), true, true).map(_makeAbsolute);\n  };\n\n  return FlipState;\n}();\n\nvar ElementState = /*#__PURE__*/function () {\n  function ElementState(element, props, simple) {\n    this.element = element;\n    this.update(props, simple);\n  }\n\n  var _proto2 = ElementState.prototype;\n\n  _proto2.isDifferent = function isDifferent(state) {\n    var b1 = this.bounds,\n        b2 = state.bounds;\n    return b1.top !== b2.top || b1.left !== b2.left || b1.width !== b2.width || b1.height !== b2.height || !this.matrix.equals(state.matrix) || this.opacity !== state.opacity || this.props && state.props && JSON.stringify(this.props) !== JSON.stringify(state.props);\n  };\n\n  _proto2.update = function update(props, simple) {\n    var self = this,\n        element = self.element,\n        getProp = gsap.getProperty(element),\n        cache = gsap.core.getCache(element),\n        bounds = element.getBoundingClientRect(),\n        bbox = element.getBBox && typeof element.getBBox === \"function\" && element.nodeName.toLowerCase() !== \"svg\" && element.getBBox(),\n        m = simple ? new Matrix2D(1, 0, 0, 1, bounds.left + _getDocScrollLeft(), bounds.top + _getDocScrollTop()) : getGlobalMatrix(element, false, false, true);\n    cache.uncache = 1; // in case there are CSS rules that affect the element. Example: https://gsap.com/community/forums/topic/44321-bug-on-fixed-position-using-flip/\n\n    self.getProp = getProp;\n    self.element = element;\n    self.id = _getID(element);\n    self.matrix = m;\n    self.cache = cache;\n    self.bounds = bounds;\n    self.isVisible = !!(bounds.width || bounds.height || bounds.left || bounds.top);\n    self.display = getProp(\"display\");\n    self.position = getProp(\"position\");\n    self.parent = element.parentNode;\n    self.x = getProp(\"x\");\n    self.y = getProp(\"y\");\n    self.scaleX = cache.scaleX;\n    self.scaleY = cache.scaleY;\n    self.rotation = getProp(\"rotation\");\n    self.skewX = getProp(\"skewX\");\n    self.opacity = getProp(\"opacity\");\n    self.width = bbox ? bbox.width : _closestTenth(getProp(\"width\", \"px\"), 0.04); // round up to the closest 0.1 so that text doesn't wrap.\n\n    self.height = bbox ? bbox.height : _closestTenth(getProp(\"height\", \"px\"), 0.04);\n    props && _recordProps(self, _memoizedProps[props] || _memoizeProps(props));\n    self.ctm = element.getCTM && element.nodeName.toLowerCase() === \"svg\" && _getCTM(element).inverse();\n    self.simple = simple || _round(m.a) === 1 && !_round(m.b) && !_round(m.c) && _round(m.d) === 1; // allows us to speed through some other tasks if it's not scale/rotated\n\n    self.uncache = 0;\n  };\n\n  return ElementState;\n}();\n\nvar FlipAction = /*#__PURE__*/function () {\n  function FlipAction(vars, batch) {\n    this.vars = vars;\n    this.batch = batch;\n    this.states = [];\n    this.timeline = batch.timeline;\n  }\n\n  var _proto3 = FlipAction.prototype;\n\n  _proto3.getStateById = function getStateById(id) {\n    var i = this.states.length;\n\n    while (i--) {\n      if (this.states[i].idLookup[id]) {\n        return this.states[i];\n      }\n    }\n  };\n\n  _proto3.kill = function kill() {\n    this.batch.remove(this);\n  };\n\n  return FlipAction;\n}();\n\nvar FlipBatch = /*#__PURE__*/function () {\n  function FlipBatch(id) {\n    this.id = id;\n    this.actions = [];\n    this._kill = [];\n    this._final = [];\n    this._abs = [];\n    this._run = [];\n    this.data = {};\n    this.state = new FlipState();\n    this.timeline = gsap.timeline();\n  }\n\n  var _proto4 = FlipBatch.prototype;\n\n  _proto4.add = function add(config) {\n    var result = this.actions.filter(function (action) {\n      return action.vars === config;\n    });\n\n    if (result.length) {\n      return result[0];\n    }\n\n    result = new FlipAction(typeof config === \"function\" ? {\n      animate: config\n    } : config, this);\n    this.actions.push(result);\n    return result;\n  };\n\n  _proto4.remove = function remove(action) {\n    var i = this.actions.indexOf(action);\n    i >= 0 && this.actions.splice(i, 1);\n    return this;\n  };\n\n  _proto4.getState = function getState(merge) {\n    var _this3 = this;\n\n    var prevBatch = _batch,\n        prevAction = _batchAction;\n    _batch = this;\n    this.state.clear();\n    this._kill.length = 0;\n    this.actions.forEach(function (action) {\n      if (action.vars.getState) {\n        action.states.length = 0;\n        _batchAction = action;\n        action.state = action.vars.getState(action);\n      }\n\n      merge && action.states.forEach(function (s) {\n        return _this3.state.add(s);\n      });\n    });\n    _batchAction = prevAction;\n    _batch = prevBatch;\n    this.killConflicts();\n    return this;\n  };\n\n  _proto4.animate = function animate() {\n    var _this4 = this;\n\n    var prevBatch = _batch,\n        tl = this.timeline,\n        i = this.actions.length,\n        finalStates,\n        endTime;\n    _batch = this;\n    tl.clear();\n    this._abs.length = this._final.length = this._run.length = 0;\n    this.actions.forEach(function (a) {\n      a.vars.animate && a.vars.animate(a);\n      var onEnter = a.vars.onEnter,\n          onLeave = a.vars.onLeave,\n          targets = a.targets,\n          s,\n          result;\n\n      if (targets && targets.length && (onEnter || onLeave)) {\n        s = new FlipState();\n        a.states.forEach(function (state) {\n          return s.add(state);\n        });\n        result = s.compare(Flip.getState(targets));\n        result.enter.length && onEnter && onEnter(result.enter);\n        result.leave.length && onLeave && onLeave(result.leave);\n      }\n    });\n\n    _makeCompsAbsolute(this._abs);\n\n    this._run.forEach(function (f) {\n      return f();\n    });\n\n    endTime = tl.duration();\n    finalStates = this._final.slice(0);\n    tl.add(function () {\n      if (endTime <= tl.time()) {\n        // only call if moving forward in the timeline (in case it's nested in a timeline that gets reversed)\n        finalStates.forEach(function (f) {\n          return f();\n        });\n\n        _forEachBatch(_this4, \"onComplete\");\n      }\n    });\n    _batch = prevBatch;\n\n    while (i--) {\n      this.actions[i].vars.once && this.actions[i].kill();\n    }\n\n    _forEachBatch(this, \"onStart\");\n\n    tl.restart();\n    return this;\n  };\n\n  _proto4.loadState = function loadState(done) {\n    done || (done = function done() {\n      return 0;\n    });\n    var queue = [];\n    this.actions.forEach(function (c) {\n      if (c.vars.loadState) {\n        var i,\n            f = function f(targets) {\n          targets && (c.targets = targets);\n          i = queue.indexOf(f);\n\n          if (~i) {\n            queue.splice(i, 1);\n            queue.length || done();\n          }\n        };\n\n        queue.push(f);\n        c.vars.loadState(f);\n      }\n    });\n    queue.length || done();\n    return this;\n  };\n\n  _proto4.setState = function setState() {\n    this.actions.forEach(function (c) {\n      return c.targets = c.vars.setState && c.vars.setState(c);\n    });\n    return this;\n  };\n\n  _proto4.killConflicts = function killConflicts(soft) {\n    this.state.interrupt(soft);\n\n    this._kill.forEach(function (state) {\n      return state.interrupt(soft);\n    });\n\n    return this;\n  };\n\n  _proto4.run = function run(skipGetState, merge) {\n    var _this5 = this;\n\n    if (this !== _batch) {\n      skipGetState || this.getState(merge);\n      this.loadState(function () {\n        if (!_this5._killed) {\n          _this5.setState();\n\n          _this5.animate();\n        }\n      });\n    }\n\n    return this;\n  };\n\n  _proto4.clear = function clear(stateOnly) {\n    this.state.clear();\n    stateOnly || (this.actions.length = 0);\n  };\n\n  _proto4.getStateById = function getStateById(id) {\n    var i = this.actions.length,\n        s;\n\n    while (i--) {\n      s = this.actions[i].getStateById(id);\n\n      if (s) {\n        return s;\n      }\n    }\n\n    return this.state.idLookup[id] && this.state;\n  };\n\n  _proto4.kill = function kill() {\n    this._killed = 1;\n    this.clear();\n    delete _batchLookup[this.id];\n  };\n\n  return FlipBatch;\n}();\n\nexport var Flip = /*#__PURE__*/function () {\n  function Flip() {}\n\n  Flip.getState = function getState(targets, vars) {\n    var state = _parseState(targets, vars);\n\n    _batchAction && _batchAction.states.push(state);\n    vars && vars.batch && Flip.batch(vars.batch).state.add(state);\n    return state;\n  };\n\n  Flip.from = function from(state, vars) {\n    vars = vars || {};\n    \"clearProps\" in vars || (vars.clearProps = true);\n    return _fromTo(state, _parseState(vars.targets || state.targets, {\n      props: vars.props || state.props,\n      simple: vars.simple,\n      kill: !!vars.kill\n    }), vars, -1);\n  };\n\n  Flip.to = function to(state, vars) {\n    return _fromTo(state, _parseState(vars.targets || state.targets, {\n      props: vars.props || state.props,\n      simple: vars.simple,\n      kill: !!vars.kill\n    }), vars, 1);\n  };\n\n  Flip.fromTo = function fromTo(fromState, toState, vars) {\n    return _fromTo(fromState, toState, vars);\n  };\n\n  Flip.fit = function fit(fromEl, toEl, vars) {\n    var v = vars ? _copy(vars, _fitReserved) : {},\n        _ref = vars || v,\n        absolute = _ref.absolute,\n        scale = _ref.scale,\n        getVars = _ref.getVars,\n        props = _ref.props,\n        runBackwards = _ref.runBackwards,\n        onComplete = _ref.onComplete,\n        simple = _ref.simple,\n        fitChild = vars && vars.fitChild && _getEl(vars.fitChild),\n        before = _parseElementState(toEl, props, simple, fromEl),\n        after = _parseElementState(fromEl, 0, simple, before),\n        inlineProps = props ? _memoizedRemoveProps[props] : _removeProps,\n        ctx = gsap.context();\n\n    props && _applyProps(v, before.props);\n\n    _recordInlineStyles(after, inlineProps);\n\n    if (runBackwards) {\n      \"immediateRender\" in v || (v.immediateRender = true);\n\n      v.onComplete = function () {\n        _applyInlineStyles(after);\n\n        onComplete && onComplete.apply(this, arguments);\n      };\n    }\n\n    absolute && _makeAbsolute(after, before);\n    v = _fit(after, before, scale || fitChild, !v.duration && props, fitChild, v.duration || getVars ? v : 0);\n    typeof vars === \"object\" && \"zIndex\" in vars && (v.zIndex = vars.zIndex);\n    ctx && !getVars && ctx.add(function () {\n      return function () {\n        return _applyInlineStyles(after);\n      };\n    });\n    return getVars ? v : v.duration ? gsap.to(after.element, v) : null;\n  };\n\n  Flip.makeAbsolute = function makeAbsolute(targetsOrStates, vars) {\n    return (targetsOrStates instanceof FlipState ? targetsOrStates : new FlipState(targetsOrStates, vars)).makeAbsolute();\n  };\n\n  Flip.batch = function batch(id) {\n    id || (id = \"default\");\n    return _batchLookup[id] || (_batchLookup[id] = new FlipBatch(id));\n  };\n\n  Flip.killFlipsOf = function killFlipsOf(targets, complete) {\n    (targets instanceof FlipState ? targets.targets : _toArray(targets)).forEach(function (t) {\n      return t && _killFlip(t._flip, complete !== false ? 1 : 2);\n    });\n  };\n\n  Flip.isFlipping = function isFlipping(target) {\n    var f = Flip.getByTarget(target);\n    return !!f && f.isActive();\n  };\n\n  Flip.getByTarget = function getByTarget(target) {\n    return (_getEl(target) || _emptyObj)._flip;\n  };\n\n  Flip.getElementState = function getElementState(target, props) {\n    return new ElementState(_getEl(target), props);\n  };\n\n  Flip.convertCoordinates = function convertCoordinates(fromElement, toElement, point) {\n    var m = getGlobalMatrix(toElement, true, true).multiply(getGlobalMatrix(fromElement));\n    return point ? m.apply(point) : m;\n  };\n\n  Flip.register = function register(core) {\n    _body = typeof document !== \"undefined\" && document.body;\n\n    if (_body) {\n      gsap = core;\n\n      _setDoc(_body);\n\n      _toArray = gsap.utils.toArray;\n      _getStyleSaver = gsap.core.getStyleSaver;\n      var snap = gsap.utils.snap(0.1);\n\n      _closestTenth = function _closestTenth(value, add) {\n        return snap(parseFloat(value) + add);\n      };\n    }\n  };\n\n  return Flip;\n}();\nFlip.version = \"3.13.0\"; // function whenImagesLoad(el, func) {\n// \tlet pending = [],\n// \t\tonLoad = e => {\n// \t\t\tpending.splice(pending.indexOf(e.target), 1);\n// \t\t\te.target.removeEventListener(\"load\", onLoad);\n// \t\t\tpending.length || func();\n// \t\t};\n// \tgsap.utils.toArray(el.tagName.toLowerCase() === \"img\" ? el : el.querySelectorAll(\"img\")).forEach(img => img.complete || img.addEventListener(\"load\", onLoad) || pending.push(img));\n// \tpending.length || func();\n// }\n\ntypeof window !== \"undefined\" && window.gsap && window.gsap.registerPlugin(Flip);\nexport { Flip as default };"], "mappings": ";AAUA,IAAI;AAAJ,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKI;AALJ,IAMI;AANJ,IAOI;AAPJ,IAQI,iBAAiB;AARrB,IASI,uBAAuB,iBAAiB;AAT5C,IAUI;AAVJ,IAWI,UAAU,SAASA,SAAQ,SAAS;AACtC,MAAI,MAAM,QAAQ,iBAAiB;AAEnC,MAAI,EAAE,kBAAkB,QAAQ,UAAU,iBAAiB,QAAQ,OAAO;AAExE,qBAAiB;AACjB,2BAAuB,iBAAiB;AAAA,EAC1C;AAEA,SAAO,IAAI,eAAe,MAAM,IAAI,aAAa;AAAA,EAAC;AAElD,SAAO;AACP,oBAAkB,IAAI,SAAS;AAE/B,MAAI,KAAK;AACP,WAAO;AACP,kBAAc,IAAI;AAClB,YAAQ,IAAI;AACZ,WAAO,KAAK,gBAAgB,8BAA8B,GAAG;AAE7D,SAAK,MAAM,YAAY;AAEvB,QAAI,KAAK,IAAI,cAAc,KAAK,GAC5B,KAAK,IAAI,cAAc,KAAK,GAC5B,OAAO,QAAQ,IAAI,QAAQ,IAAI;AAEnC,QAAI,QAAQ,KAAK,aAAa;AAC5B,WAAK,YAAY,EAAE;AACnB,SAAG,YAAY,EAAE;AACjB,SAAG,aAAa,SAAS,gDAAgD;AACzE,sBAAgB,GAAG,iBAAiB;AACpC,WAAK,YAAY,EAAE;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AACT;AA/CA,IAgDI,qBAAqB,SAASC,oBAAmB,GAAG;AAEtD,MAAI,GAAG;AAEP,SAAO,KAAK,MAAM,OAAO;AACvB,YAAQ,EAAE;AACV,aAAS,MAAM,WAAW,MAAM,IAAI,GAAG,GAAG;AAE1C,QAAI,SAAS,CAAC,MAAM,UAAU,CAAC,MAAM,UAAU,MAAM,iBAAiB;AACpE,YAAM,SAAS,MAAM,SAAS;AAC9B,YAAM,gBAAgB,GAAG,KAAK;AAC9B,UAAI,EAAE,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK;AAAA,IAChC;AAEA,QAAI,EAAE;AAAA,EACR;AAEA,SAAO;AACT;AAlEA,IAqFA,YAAY,CAAC;AArFb,IAuFA,YAAY,CAAC;AAvFb,IAwFI,mBAAmB,SAASC,oBAAmB;AACjD,SAAO,KAAK,eAAe,KAAK,aAAa,YAAY,aAAa,MAAM,aAAa;AAC3F;AA1FA,IA2FI,oBAAoB,SAASC,qBAAoB;AACnD,SAAO,KAAK,eAAe,KAAK,cAAc,YAAY,cAAc,MAAM,cAAc;AAC9F;AA7FA,IA8FI,YAAY,SAASC,WAAU,SAAS;AAC1C,SAAO,QAAQ,qBAAqB,QAAQ,UAAU,IAAI,YAAY,MAAM,QAAQ,UAAU;AAChG;AAhGA,IAiGI,WAAW,SAASC,UAAS,SAAS;AACxC,MAAI,KAAK,iBAAiB,OAAO,EAAE,aAAa,SAAS;AACvD,WAAO;AAAA,EACT;AAEA,YAAU,QAAQ;AAElB,MAAI,WAAW,QAAQ,aAAa,GAAG;AAErC,WAAOA,UAAS,OAAO;AAAA,EACzB;AACF;AA5GA,IA6GI,iBAAiB,SAASC,gBAAe,SAAS,GAAG;AACvD,MAAI,QAAQ,eAAe,QAAQ,QAAQ,OAAO,IAAI;AACpD,QAAI,MAAM,UAAU,OAAO,GACvB,KAAK,MAAM,IAAI,aAAa,OAAO,KAAK,+BAA+B,gCACvE,OAAO,MAAM,IAAI,SAAS,MAAM,OAChC,IAAI,MAAM,IAAI,IAAI,KAClB,IAAI,MAAM,IAAI,MAAM,GACpB,MAAM,2EACN,IAAI,KAAK,kBAAkB,KAAK,gBAAgB,GAAG,QAAQ,UAAU,MAAM,GAAG,IAAI,IAAI,KAAK,cAAc,IAAI;AAEjH,QAAI,GAAG;AACL,UAAI,CAAC,KAAK;AACR,YAAI,CAAC,eAAe;AAClB,0BAAgBA,gBAAe,OAAO;AACtC,wBAAc,MAAM,UAAU;AAAA,QAChC;AAEA,UAAE,MAAM,UAAU,MAAM,kCAAkC,IAAI,aAAa,IAAI;AAE/E,sBAAc,YAAY,CAAC;AAAA,MAC7B,OAAO;AACL,0BAAkB,gBAAgBA,gBAAe,OAAO;AACxD,UAAE,aAAa,SAAS,IAAI;AAC5B,UAAE,aAAa,UAAU,IAAI;AAC7B,UAAE,aAAa,aAAa,eAAe,IAAI,MAAM,IAAI,GAAG;AAE5D,sBAAc,YAAY,CAAC;AAAA,MAC7B;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,QAAM;AACR;AA/IA,IAgJI,eAAe,SAASC,cAAa,GAAG;AAE1C,MAAI,IAAI,IAAI,SAAS,GACjB,IAAI;AAER,SAAO,IAAI,EAAE,eAAe,KAAK;AAC/B,MAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM;AAAA,EAChC;AAEA,SAAO;AACT;AA1JA,IA2JI,UAAU,SAASC,SAAQ,KAAK;AAClC,MAAI,IAAI,IAAI,OAAO,GACf;AAEJ,MAAI,CAAC,GAAG;AAEN,gBAAY,IAAI,MAAM,cAAc;AACpC,QAAI,MAAM,cAAc,IAAI;AAE5B,QAAI,YAAY,IAAI;AACpB,QAAI,KAAK,OAAO;AAChB,QAAI,YAAY,IAAI;AACpB,gBAAY,IAAI,MAAM,cAAc,IAAI,YAAY,IAAI,MAAM,eAAe,eAAe,QAAQ,YAAY,KAAK,EAAE,YAAY,CAAC;AAAA,EACtI;AAEA,SAAO,KAAK,gBAAgB,MAAM;AACpC;AA3KA,IA4KI,iBAAiB,SAASC,gBAAe,SAAS,eAAe;AACnE,MAAI,MAAM,UAAU,OAAO,GACvB,YAAY,YAAY,KACxB,WAAW,MAAM,YAAY,WAC7B,SAAS,QAAQ,YACjB,aAAa,UAAU,CAAC,OAAO,OAAO,cAAc,OAAO,WAAW,cAAc,OAAO,aAAa,QACxG,WACA,GACA,GACA,GACA,GACA;AAEJ,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT;AAEA,WAAS,UAAU,SAAS,KAAK,eAAe,SAAS,CAAC,GAAG,eAAe,SAAS,CAAC,GAAG,eAAe,SAAS,CAAC,CAAC;AACnH,cAAY,MAAM,gBAAgB;AAElC,MAAI,KAAK;AACP,QAAI,WAAW;AACb,UAAI,QAAQ,OAAO;AACnB,UAAI,CAAC,EAAE,IAAI,EAAE;AACb,UAAI,CAAC,EAAE,IAAI,EAAE;AACb,UAAI;AAAA,IACN,WAAW,QAAQ,SAAS;AAC1B,UAAI,QAAQ,QAAQ;AACpB,UAAI,QAAQ,YAAY,QAAQ,UAAU,UAAU,CAAC;AAErD,UAAI,CAAC,EAAE,gBAAgB,kBAAkB,EAAE,gBAAgB,IAAI,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE;AAE9F,UAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AACxB,UAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,IAC1B,OAAO;AAEL,UAAI,IAAI,SAAS;AACjB,UAAI,IAAI;AAAA,IACV;AAEA,QAAI,iBAAiB,QAAQ,QAAQ,YAAY,MAAM,KAAK;AAC1D,UAAI,IAAI;AAAA,IACV;AAEA,KAAC,YAAY,MAAM,QAAQ,YAAY,SAAS;AAChD,cAAU,aAAa,aAAa,YAAY,EAAE,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE,IAAI,OAAO,EAAE,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,GAAG;AAAA,EACnI,OAAO;AACL,QAAI,IAAI;AAER,QAAI,eAAe;AAEjB,UAAI,QAAQ;AACZ,UAAI;AAEJ,aAAO,MAAM,IAAI,EAAE,eAAe,MAAM,KAAK,EAAE,YAAY;AACzD,aAAK,KAAK,iBAAiB,CAAC,EAAE,cAAc,IAAI,IAAI,SAAS,GAAG;AAC9D,cAAI,EAAE;AACN,cAAI,EAAE;AACN,cAAI;AAAA,QACN;AAAA,MACF;AAAA,IACF;AAEA,SAAK,KAAK,iBAAiB,OAAO;AAElC,QAAI,GAAG,aAAa,cAAc,GAAG,aAAa,SAAS;AACzD,UAAI,QAAQ;AAEZ,aAAO,UAAU,WAAW,GAAG;AAE7B,aAAK,OAAO,cAAc;AAC1B,aAAK,OAAO,aAAa;AACzB,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF;AAEA,QAAI,UAAU;AACd,MAAE,MAAM,QAAQ,YAAY,IAAI;AAChC,MAAE,OAAO,QAAQ,aAAa,IAAI;AAClC,MAAE,cAAc,IAAI,GAAG,cAAc;AACrC,MAAE,oBAAoB,IAAI,GAAG,oBAAoB;AAMjD,MAAE,WAAW,GAAG,aAAa,UAAU,UAAU;AACjD,eAAW,YAAY,SAAS;AAAA,EAClC;AAEA,SAAO;AACT;AAvQA,IAwQI,aAAa,SAASC,YAAW,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AACxD,IAAE,IAAI;AACN,IAAE,IAAI;AACN,IAAE,IAAI;AACN,IAAE,IAAI;AACN,IAAE,IAAI;AACN,IAAE,IAAI;AACN,SAAO;AACT;AAEO,IAAI,WAAwB,WAAY;AAC7C,WAASC,UAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAClC,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,QAAI,MAAM,QAAQ;AAChB,UAAI;AAAA,IACN;AAEA,eAAW,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAAA,EACnC;AAEA,MAAI,SAASA,UAAS;AAEtB,SAAO,UAAU,SAAS,UAAU;AAClC,QAAI,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,cAAc,IAAI,IAAI,IAAI,KAAK;AACnC,WAAO,WAAW,MAAM,IAAI,aAAa,CAAC,IAAI,aAAa,CAAC,IAAI,aAAa,IAAI,cAAc,IAAI,IAAI,IAAI,KAAK,aAAa,EAAE,IAAI,IAAI,IAAI,KAAK,WAAW;AAAA,EAC7J;AAEA,SAAO,WAAW,SAAS,SAAS,QAAQ;AAC1C,QAAI,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,KAAK,OAAO,GACZ,KAAK,OAAO,GACZ,KAAK,OAAO,GACZ,KAAK,OAAO,GACZ,KAAK,OAAO,GACZ,KAAK,OAAO;AAChB,WAAO,WAAW,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,EACtI;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,WAAO,IAAIA,UAAS,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,EACpE;AAEA,SAAO,SAAS,SAAS,OAAO,QAAQ;AACtC,QAAI,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK;AACb,WAAO,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO,KAAK,MAAM,OAAO;AAAA,EAChH;AAEA,SAAO,QAAQ,SAAS,MAAM,OAAO,WAAW;AAC9C,QAAI,cAAc,QAAQ;AACxB,kBAAY,CAAC;AAAA,IACf;AAEA,QAAI,IAAI,MAAM,GACV,IAAI,MAAM,GACV,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK,GACT,IAAI,KAAK;AACb,cAAU,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AACnC,cAAU,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK;AACnC,WAAO;AAAA,EACT;AAEA,SAAOA;AACT,EAAE;AAQK,SAAS,gBAAgB,SAAS,SAAS,eAAe,sBAAsB;AAErF,MAAI,CAAC,WAAW,CAAC,QAAQ,eAAe,QAAQ,QAAQ,OAAO,GAAG,oBAAoB,SAAS;AAC7F,WAAO,IAAI,SAAS;AAAA,EACtB;AAEA,MAAI,aAAa,mBAAmB,OAAO,GACvC,MAAM,UAAU,OAAO,GACvB,QAAQ,MAAM,YAAY,WAC1B,YAAY,eAAe,SAAS,aAAa,GACjD,KAAK,MAAM,CAAC,EAAE,sBAAsB,GACpC,KAAK,MAAM,CAAC,EAAE,sBAAsB,GACpC,KAAK,MAAM,CAAC,EAAE,sBAAsB,GACpC,SAAS,UAAU,YACnB,UAAU,CAAC,wBAAwB,SAAS,OAAO,GACnD,IAAI,IAAI,UAAU,GAAG,OAAO,GAAG,QAAQ,MAAM,GAAG,MAAM,GAAG,OAAO,MAAM,GAAG,OAAO,GAAG,QAAQ,MAAM,GAAG,MAAM,GAAG,OAAO,KAAK,GAAG,QAAQ,UAAU,IAAI,kBAAkB,IAAI,GAAG,OAAO,UAAU,IAAI,iBAAiB,EAAE;AAEvN,SAAO,YAAY,SAAS;AAE5B,MAAI,YAAY;AACd,SAAK,WAAW;AAEhB,WAAO,MAAM;AACX,WAAK,WAAW,EAAE;AAClB,SAAG,SAAS,GAAG,SAAS;AACxB,SAAG,gBAAgB,GAAG,EAAE;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO,UAAU,EAAE,QAAQ,IAAI;AACjC;;;ACjZA,IAAI,MAAM;AAAV,IACI;AADJ,IAEI;AAFJ,IAGI;AAHJ,IAII;AAJJ,IAKIC;AALJ,IAMI;AANJ,IAOI;AAPJ,IAQI,gBAAgB,SAASC,eAAc,OAAO,MAAM;AACtD,SAAO,MAAM,QAAQ,QAAQ,SAAU,GAAG;AACxC,WAAO,EAAE,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,EAAE,CAAC;AAAA,EACvC,CAAC;AACH;AAZA,IAaI,eAAe,CAAC;AAbpB,IAcI,WAAW,MAAM,KAAK;AAd1B,IAeI,WAAW,KAAK,KAAK;AAfzB,IAgBI,YAAY,CAAC;AAhBjB,IAiBI,oBAAoB,CAAC;AAjBzB,IAkBI,uBAAuB,CAAC;AAlB5B,IAmBI,eAAe,SAASC,cAAa,MAAM;AAC7C,SAAO,OAAO,SAAS,WAAW,KAAK,MAAM,GAAG,EAAE,KAAK,EAAE,EAAE,MAAM,GAAG,IAAI;AAC1E;AArBA,IAuBA,aAAa,aAAa,2DAA2D;AAvBrF,IAwBI,eAAe,aAAa,+GAA+G;AAxB/I,IAyBI,SAAS,SAASC,QAAO,QAAQ;AACnC,SAAO,SAAS,MAAM,EAAE,CAAC,KAAK,QAAQ,KAAK,sBAAsB,MAAM;AACzE;AA3BA,IA4BI,SAAS,SAASC,QAAO,OAAO;AAClC,SAAO,KAAK,MAAM,QAAQ,GAAK,IAAI,OAAS;AAC9C;AA9BA,IA+BI,eAAe,SAASC,cAAa,SAAS,WAAW,QAAQ;AACnE,SAAO,QAAQ,QAAQ,SAAU,IAAI;AACnC,WAAO,GAAG,UAAU,MAAM,EAAE,SAAS;AAAA,EACvC,CAAC;AACH;AAnCA,IAoCI,YAAY;AAAA,EACd,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU;AAAA,EACV,OAAO;AAAA,EACP,SAAS;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,iBAAiB;AACnB;AA/DA,IAgEI,eAAe;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,OAAO;AACT;AAzEA,IA0EI,iBAAiB,SAASC,gBAAe,GAAG;AAC9C,SAAO,EAAE,QAAQ,YAAY,KAAK,EAAE,YAAY;AAClD;AA5EA,IA6EI,QAAQ,SAASC,OAAM,KAAK,SAAS;AACvC,MAAI,SAAS,CAAC,GACV;AAEJ,OAAK,KAAK,KAAK;AACb,YAAQ,CAAC,MAAM,OAAO,CAAC,IAAI,IAAI,CAAC;AAAA,EAClC;AAEA,SAAO;AACT;AAtFA,IAuFI,iBAAiB,CAAC;AAvFtB,IAwFI,gBAAgB,SAASC,eAAc,OAAO;AAChD,MAAI,IAAI,eAAe,KAAK,IAAI,aAAa,KAAK;AAElD,uBAAqB,KAAK,IAAI,EAAE,OAAO,YAAY;AACnD,SAAO;AACT;AA7FA,IA8FI,0BAA0B,SAASC,yBAAwB,IAAI;AAEjE,MAAI,QAAQ,GAAG,SAAS,KAAK,KAAK,SAAS,EAAE;AAE7C,MAAI,MAAM,YAAY,KAAK,OAAO,OAAO;AACvC,WAAO,MAAM;AAAA,EACf;AAEA,QAAM,UAAU,KAAK,OAAO;AAC5B,SAAO,MAAM,UAAU,gBAAgB,IAAI,MAAM,OAAO,IAAI;AAC9D;AAxGA,IAyGI,eAAe,SAASC,cAAa,IAAI,QAAQ,OAAO;AAC1D,MAAI,UAAU,QAAQ;AACpB,YAAQ;AAAA,EACV;AAGA,MAAI,SAAS,GAAG,YACZ,MAAM,MAAO,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,KAAK,IAClD,IAAI,SAAS,CAAC,MAAM,MAAM;AAE9B,SAAO,IAAI;AACT,SAAK;AACL,SAAK,GAAG;AAAA,EACV;AAEA,SAAO,SAAS,IAAIA,cAAa,QAAQ,QAAQ,QAAQ,CAAC,IAAI;AAChE;AAzHA,IA0HI,mBAAmB,SAASC,kBAAiB,OAAO,QAAQ,YAAY;AAC1E,QAAM,QAAQ,SAAU,MAAM;AAC5B,WAAO,KAAK,IAAI,aAAa,aAAa,KAAK,UAAU,KAAK,GAAG,MAAM;AAAA,EACzE,CAAC;AACD,QAAM,KAAK,SAAU,IAAI,IAAI;AAC3B,WAAO,GAAG,IAAI,GAAG;AAAA,EACnB,CAAC;AACD,SAAO;AACT;AAlIA,IAmII,sBAAsB,SAASC,qBAAoB,SAAS,OAAO;AAErE,MAAI,QAAQ,QAAQ,QAAQ,OACxB,IAAI,QAAQ,MAAM,QAAQ,OAAO,CAAC,GAClC,IAAI,MAAM,QACV,GACA;AAEJ,SAAO,KAAK;AACV,QAAI,MAAM,CAAC;AACX,QAAI,MAAM,CAAC,KAAK,MAAM,iBAAiB,CAAC;AACxC,MAAE,KAAK,IAAI,IAAI,kBAAkB,CAAC,MAAM,kBAAkB,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC;AAAA,EACtF;AAEA,SAAO;AACT;AAlJA,IAmJI,qBAAqB,SAASC,oBAAmB,OAAO;AAC1D,MAAI,MAAM,MAAM,KACZ,QAAQ,MAAM,QAAQ,OACtB,IAAI;AACR,QAAM,MAAM,UAAU;AAEtB,SAAO,IAAI,IAAI,QAAQ,KAAK,GAAG;AAC7B,QAAI,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,MAAM,eAAe,IAAI,CAAC,CAAC;AAAA,EACvE;AAEA,MAAI,CAAC,IAAI,IAAI,QAAQ,WAAW,IAAI,CAAC,KAAK,MAAM,WAAW;AAEzD,UAAM,eAAe,WAAW;AAChC,UAAM,eAAe,OAAO;AAC5B,UAAM,eAAe,QAAQ;AAAA,EAC/B;AACF;AAnKA,IAoKI,kBAAkB,SAASC,iBAAgB,OAAO,gBAAgB;AACpE,QAAM,QAAQ,SAAU,GAAG;AACzB,WAAO,EAAE,EAAE,MAAM,UAAU;AAAA,EAC7B,CAAC;AACD,oBAAkB,MAAM,YAAY,QAAQ,kBAAkB;AAChE;AAzKA,IA0KI,iBAAiB,wEAAwE,MAAM,GAAG;AA1KtG,IA4KA,gBAAgB,SAASC,eAAc,SAAS,cAAc,aAAa;AACzE,MAAI,UAAU,QAAQ,SAClB,QAAQ,QAAQ,OAChB,SAAS,QAAQ,QACjB,UAAU,QAAQ,SAClB,UAAU,QAAQ,SAClB,QAAQ,QAAQ,OAChB,IAAI,GACJ,QACA,eACA;AACJ,SAAO,iBAAiB,aAAa,eAAe;AAEpD,MAAI,UAAU,gBAAgB,GAAG;AAC/B,WAAO,KAAK,KAAK;AAAA,MACf,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,IACN,CAAC;AAED,WAAO,OAAO,KAAK,WAAY;AAC7B,cAAQ,QAAQ,MAAM,UAAU,MAAM,mBAAmB,OAAO;AAAA,IAClE,CAAC;AAED,WAAO;AAAA,EACT;AAEA,kBAAgB,QAAQ,SAAS,MAAM;AAEvC,MAAI,CAAC,QAAQ,aAAa,eAAe;AACvC,sBAAkB,oBAAoB,SAAS,CAAC,SAAS,CAAC,EAAE,UAAU,aAAa;AACnF,YAAQ,SAAS,aAAa;AAC9B,YAAQ,QAAQ,QAAQ,QAAQ,SAAS,aAAa;AACtD,YAAQ,SAAS,SAAS,QAAQ,UAAU,aAAa;AAAA,EAC3D;AAEA,sBAAoB,SAAS,cAAc;AAE3C,OAAK,OAAO,iBAAiB,OAAO;AAEpC,SAAO,KAAK;AACV,UAAM,eAAe,CAAC,CAAC,IAAI,GAAG,eAAe,CAAC,CAAC;AAAA,EACjD;AAEA,QAAM,WAAW;AACjB,QAAM,aAAa;AACnB,QAAM,WAAW;AACjB,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,SAAS;AACxB,QAAM,QAAQ,MAAM,MAAM;AAC1B,QAAM,SAAS,MAAM,OAAO;AAE5B,MAAI,SAAS;AACX,aAAS,IAAI,aAAa,OAAO;AAAA,EACnC,OAAO;AAEL,aAAS,MAAM,SAAS,SAAS;AACjC,WAAO,WAAW;AAElB,QAAI,QAAQ,QAAQ;AAClB,UAAI,SAAS,QAAQ,sBAAsB;AAC3C,aAAO,SAAS,IAAI,SAAS,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,kBAAkB,GAAG,OAAO,MAAM,iBAAiB,CAAC;AAAA,IAC7G,OAAO;AACL,aAAO,SAAS,gBAAgB,SAAS,OAAO,OAAO,IAAI;AAAA,IAC7D;AAAA,EACF;AAEA,WAAS,KAAK,QAAQ,SAAS,IAAI;AACnC,UAAQ,IAAI,cAAc,OAAO,GAAG,IAAI;AACxC,UAAQ,IAAI,cAAc,OAAO,GAAG,IAAI;AACxC,SAAO;AACT;AApPA,IAqPI,eAAe,SAASC,cAAa,OAAO,SAAS;AACvD,MAAI,YAAY,MAAM;AACpB,cAAU,SAAS,OAAO;AAC1B,YAAQ,MAAM,OAAO,SAAU,GAAG;AAChC,UAAI,QAAQ,SAAS,EAAE,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,MAAM,IAAI;AAC1D,eAAO;AAAA,MACT,OAAO;AACL,UAAE,EAAE,MAAM,gBAAgB,CAAC;AAG3B,YAAI,EAAE,EAAE,WAAW;AACjB,YAAE,EAAE,MAAM,QAAQ,EAAE,EAAE,QAAQ;AAE9B,YAAE,EAAE,MAAM,SAAS,EAAE,EAAE,SAAS;AAAA,QAClC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAzQA,IA0QI,qBAAqB,SAASC,oBAAmB,OAAO;AAC1D,SAAO,iBAAiB,OAAO,IAAI,EAAE,QAAQ,SAAU,GAAG;AACxD,YAAQ,EAAE,EAAE,aAAa,EAAE,EAAE,cAAc,cAAc,EAAE,KAAK,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;AAAA,EACvF,CAAC;AACH;AA9QA,IA+QI,sBAAsB,SAASC,qBAAoB,OAAO,OAAO;AACnE,SAAO,SAAS,MAAM,SAAS,mBAAmB,KAAK,EAAE,EAAE,KAAK,MAAM,cAAc,CAAC;AACvF;AAjRA,IAkRI,qBAAqB,SAASC,oBAAmB,UAAU,OAAO,QAAQ,OAAO;AACnF,SAAO,oBAAoB,eAAe,WAAW,oBAAoB,YAAY,oBAAoB,UAAU,KAAK,IAAI,IAAI,aAAa,OAAO,aAAa,WAAW,OAAO,QAAQ,KAAK,QAAQ,KAAK,WAAW,YAAY,IAAI,UAAU,OAAO,MAAM;AACjQ;AApRA,IAqRI,eAAe,SAASC,cAAa,SAAS,OAAO;AACvD,MAAI,UAAU,KAAK,YAAY,QAAQ,SAAS,MAAM,QAAQ,GAC1D,MAAM,QAAQ,QAAQ,CAAC,GACvB,IAAI,MAAM;AAEd,SAAO,KAAK;AACV,QAAI,MAAM,CAAC,CAAC,KAAK,QAAQ,MAAM,CAAC,CAAC,IAAI,IAAI,KAAK;AAAA,EAChD;AAEA,MAAI,WAAW,IAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACtD,SAAO;AACT;AAhSA,IAiSI,cAAc,SAASC,aAAY,SAAS,OAAO;AACrD,MAAI,QAAQ,QAAQ,SAAS,SAE7B;AAEA,OAAK,KAAK,OAAO;AACf,UAAM,CAAC,IAAI,MAAM,CAAC;AAAA,EACpB;AACF;AAzSA,IA0SI,SAAS,SAASC,QAAO,IAAI;AAC/B,MAAI,KAAK,GAAG,aAAa,cAAc;AACvC,QAAM,GAAG,aAAa,gBAAgB,KAAK,UAAU,KAAK;AAC1D,SAAO;AACT;AA9SA,IA+SI,6BAA6B,SAASC,4BAA2B,UAAU;AAC7E,SAAO,SAAS,IAAI,SAAU,SAAS;AACrC,WAAO,QAAQ;AAAA,EACjB,CAAC;AACH;AAnTA,IAoTI,kBAAkB,SAASC,iBAAgB,UAAU,UAAU,IAAI;AACrE,SAAO,YAAY,SAAS,UAAU,GAAG,IAAI,SAAS,2BAA2B,QAAQ,GAAG,IAAI,IAAI,UAAU,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC;AACtI;AAtTA,IAuTI,OAAO,SAASC,MAAK,WAAW,SAAS,OAAO,YAAY,UAAU,MAAM;AAC9E,MAAI,UAAU,UAAU,SACpB,QAAQ,UAAU,OAClB,SAAS,UAAU,QACnB,IAAI,UAAU,GACd,IAAI,UAAU,GACd,QAAQ,QAAQ,OAChB,SAAS,QAAQ,QACjB,SAAS,QAAQ,QACjB,SAAS,QAAQ,QACjB,WAAW,QAAQ,UACnB,SAAS,QAAQ,QACjB,SAAS,QAAQ,kBAAkB,eAAe,SAAS,wBAAwB,GACnF,iBAAiB,WACjB,kBAAkB,QAAQ,QAC1B,IAAI,gBAAgB,GACpB,IAAI,gBAAgB,GACpB,OAAO,UAAU,OAAO,UAAU,OAAO,SAAS,UAAU,OAAO,WAAW,OAAO,UAAU,UAAU,WAAW,UAAU,UAAU,WAAW,UAAU,UAAU,aAAa,UACpL,SAAS,CAAC,QAAQ,UAAU,UAAU,QAAQ,UAAU,CAAC,UACzD,OACA,WACA,SACA,SACA,cACA,QACA;AAEJ,MAAI,UAAU,CAAC,QAAQ;AACrB,aAAS,SAAS;AAClB,eAAW,QAAQ;AAAA,EACrB,OAAO;AACL,mBAAe,wBAAwB,MAAM;AAC7C,aAAS,aAAa,MAAM,EAAE,SAAS,QAAQ,MAAM,QAAQ,OAAO,MAAM,EAAE,SAAS,QAAQ,GAAG,IAAI,QAAQ,MAAM;AAElH,eAAW,OAAO,KAAK,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,QAAQ;AAC3D,YAAQ,OAAO,KAAK,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,WAAW,QAAQ,IAAI;AAEvE,aAAS,KAAK,KAAK,KAAK,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC;AAChE,aAAS,KAAK,KAAK,KAAK,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,QAAQ,QAAQ;AAE7F,QAAI,UAAU;AACZ,iBAAW,SAAS,QAAQ,EAAE,CAAC;AAC/B,gBAAU,KAAK,YAAY,QAAQ;AACnC,aAAO,SAAS,WAAW,OAAO,SAAS,YAAY,cAAc,SAAS,QAAQ;AACtF,uBAAiB;AAAA,QACf,QAAQ,QAAQ,QAAQ;AAAA,QACxB,QAAQ,QAAQ,QAAQ;AAAA,QACxB,OAAO,OAAO,KAAK,QAAQ,KAAK,KAAK,WAAW,QAAQ,SAAS,IAAI,CAAC,CAAC;AAAA,QACvE,QAAQ,OAAO,KAAK,SAAS,WAAW,QAAQ,UAAU,IAAI,CAAC;AAAA,MACjE;AAAA,IACF;AAEA,UAAM,WAAW,WAAW;AAC5B,UAAM,QAAQ,QAAQ;AAAA,EACxB;AAEA,MAAI,OAAO;AACT,cAAU,UAAU,eAAe,SAAS,CAAC,eAAe,QAAQ,IAAI,QAAQ,eAAe;AAE/F,cAAU,WAAW,eAAe,UAAU,CAAC,eAAe,SAAS,IAAI,SAAS,eAAe;AACnG,UAAM,SAAS;AACf,UAAM,SAAS;AAAA,EACjB,OAAO;AACL,YAAQ,cAAc,QAAQ,SAAS,eAAe,QAAQ,CAAC;AAC/D,aAAS,cAAc,SAAS,SAAS,eAAe,QAAQ,CAAC;AACjE,YAAQ,MAAM,QAAQ,QAAQ;AAC9B,YAAQ,MAAM,SAAS,SAAS;AAAA,EAClC;AAMA,gBAAc,YAAY,SAAS,QAAQ,KAAK;AAEhD,MAAI,UAAU,CAAC,QAAQ;AACrB,SAAK,IAAI,UAAU,OAAO;AAC1B,SAAK,IAAI,UAAU,OAAO;AAAA,EAC5B,WAAW,QAAQ,WAAW,QAAQ,QAAQ;AAC5C,UAAM,gBAAgB,GAAG,KAAK;AAC9B,aAAS,gBAAgB,YAAY,SAAS,OAAO,OAAO,IAAI;AAChE,gBAAY,aAAa,MAAM;AAAA,MAC7B,GAAG,OAAO;AAAA,MACV,GAAG,OAAO;AAAA,IACZ,CAAC;AACD,cAAU,aAAa,MAAM;AAAA,MAC3B,GAAG;AAAA,MACH,GAAG;AAAA,IACL,CAAC;AACD,SAAK,QAAQ,IAAI,UAAU;AAC3B,SAAK,QAAQ,IAAI,UAAU;AAAA,EAC7B,OAAO;AAEL,iBAAa,IAAI,aAAa,IAAI;AAClC,cAAU,aAAa,MAAM;AAAA,MAC3B,GAAG,IAAI,UAAU,OAAO;AAAA,MACxB,GAAG,IAAI,UAAU,OAAO;AAAA,IAC1B,CAAC;AACD,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACf;AAEA,MAAI,cAAc,GAAG,IAAI;AACzB,MAAI,cAAc,GAAG,IAAI;AAEzB,MAAI,QAAQ,EAAE,gBAAgB,eAAe;AAE3C,cAAU,OAAO,OAAO;AAAA,EAC1B,OAAO;AAEL,UAAM,IAAI,IAAI;AACd,UAAM,IAAI,IAAI;AACd,UAAM,gBAAgB,GAAG,KAAK;AAAA,EAChC;AAEA,MAAI,MAAM;AACR,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,WAAW;AAChB,SAAK,QAAQ;AAEb,QAAI,OAAO;AACT,WAAK,SAAS;AACd,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,WAAK,QAAQ;AACb,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAEA,SAAO,QAAQ;AACjB;AA1bA,IA2bI,cAAc,SAASC,aAAY,gBAAgB,MAAM;AAC3D,SAAO,0BAA0B,YAAY,iBAAiB,IAAI,UAAU,gBAAgB,IAAI;AAClG;AA7bA,IA8bI,sBAAsB,SAASC,qBAAoB,SAAS,WAAW,IAAI;AAC7E,MAAI,MAAM,QAAQ,SAAS,EAAE,GACzB,MAAM,QAAQ,IAAI,EAAE;AACxB,SAAO,IAAI,cAAc,EAAE,UAAU,gBAAgB,IAAI,OAAO,KAAK,KAAK,aAAa,CAAC,IAAI,aAAa,MAAM;AACjH;AAlcA,IAmcI,eAAe,CAAC;AAncpB,IAocI,aAAa,mCAAmC,MAAM,GAAG;AApc7D,IAqcI;AArcJ,IAscI,kBAAkB,SAASC,iBAAgB,MAAM;AAEnD,MAAI,SAAS,aAAa;AACxB,QAAI,IAAI5B,OAAM,OACV,IAAIA,OAAM,gBAAgB,OAAO,YACjC,IAAIA,OAAM,iBAAiB,OAAO,aAClC,IAAI;AAER,QAAI,SAAS,KAAK,IAAI;AACpB,aAAO,KAAK;AACV,qBAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AAAA,MACnC;AAEA,UAAI,GAAG;AACL,UAAE,QAAQA,OAAM,cAAc;AAC9B,UAAE,YAAY;AAAA,MAChB;AAEA,UAAI,GAAG;AACL,UAAE,SAASA,OAAM,eAAe;AAChC,UAAE,YAAY;AAAA,MAChB;AAEA,oBAAc;AAAA,IAChB,WAAW,aAAa;AACtB,aAAO,KAAK;AACV,qBAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,eAAe,eAAe,WAAW,CAAC,CAAC,CAAC;AAAA,MACvG;AAEA,oBAAc;AAAA,IAChB;AAAA,EACF;AACF;AAteA,IAueI,UAAU,SAAS6B,SAAQ,WAAW,SAAS,MAAM,UAAU;AAEjE,uBAAqB,aAAa,mBAAmB,aAAa,QAAQ,KAAK,2BAA2B;AAC1G,SAAO,QAAQ,CAAC;AAEhB,MAAI,QAAQ,MACR,aAAa,MAAM,YACnB,UAAU,MAAM,SAChB,UAAU,MAAM,SAChB,WAAW,MAAM,UACjB,kBAAkB,MAAM,iBACxB,SAAS,MAAM,QACf,QAAQ,MAAM,OACd,SAAS,MAAM,QACf,SAAS,MAAM,QACf,cAAc,MAAM,aACpB,OAAO,MAAM,MACb,cAAc,MAAM,aACpB,SAAS,MAAM,QACf,UAAU,MAAM,QAChB,QAAQ,MAAM,OACd,OAAO,MAAM,MACb,UAAU,MAAM,SAChB,OAAO,MAAM,MACb,QAAQ,MAAM,OACd,SAAS,WAAW,OAAO,OAAO,WAAW,OAC7C,YAAY,MAAM,MAAM,SAAS,GACjC,YAAY,KAAK,SAAS;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC,GACG,iBAAiB,WACjB,WAAW,CAAC,GACZ,UAAU,CAAC,GACX,QAAQ,CAAC,GACT,iBAAiB,CAAC,GAClB,UAAU,SAAS,OAAO,IAAI,QAAQ,GACtC,WAAW,OAAO,SAAS,aAAa,OAAO,WAAY;AAC7D,WAAO;AAAA,EACT,GACI,cAAc,UAAU,eAAe,QAAQ,aAC/C,UAAU,UAAU,aAAa,IAAI,OAAO,MAAM,GAClD,GACA,GACA,SACA,GACA,IACA,MACA,OACA,SACA,aACA,UACA,QACA,KACA,GACA;AAGJ,OAAK,KAAK,QAAQ,UAAU;AAC1B,aAAS,CAAC,QAAQ,IAAI,CAAC,IAAI,QAAQ,SAAS,CAAC,IAAI,oBAAoB,SAAS,WAAW,CAAC;AAC1F,SAAK,OAAO;AACZ,eAAW,UAAU,SAAS,CAAC;AAC/B,cAAU,IAAI,CAAC,KAAK,OAAO,SAAS,YAAY,UAAU,IAAI,CAAC,EAAE,aAAa,CAAC,OAAO,eAAe,WAAW,UAAU,IAAI,CAAC;AAE/H,QAAI,UAAU;AACZ,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,IAAI,SAAS,YAAY,KAAK,IAAI,OAAO,YAAY,IAAI;AAAA,MAC3D;AACA,YAAM,KAAK,IAAI;AAEf,UAAI,KAAK,IAAI;AACX,YAAI,KAAK,KAAK,GAAG;AACf,eAAK,IAAI;AACT,eAAK,IAAI;AAAA,QACX;AAGA,uBAAe,oBAAoB,KAAK,GAAG,QAAQ,qBAAqB,KAAK,IAAI,YAAY;AAC7F,gBAAQ,MAAM,KAAK,KAAK,OAAO;AAAA,UAC7B,GAAG,SAAS;AAAA,UACZ,GAAG,KAAK;AAAA,UACR,GAAG,KAAK;AAAA,UACR,IAAI,CAAC,KAAK;AAAA,UACV,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAEA,SAAG,QAAQ,SAAS,QAAQ,QAAQ,SAAS,OAAO,WAAW;AAAA,IACjE,WAAW,OAAO,WAAW;AAC3B,YAAM,KAAK;AAAA,QACT,GAAG;AAAA,QACH,GAAG,MAAM,QAAQ;AAAA,UACf,WAAW;AAAA,QACb,CAAC;AAAA,QACD,GAAG;AAAA,QACH,IAAI;AAAA,QACJ,UAAU;AAAA,MACZ,CAAC;AAED,SAAG,QAAQ,SAAS,OAAO,WAAW;AAAA,IACxC;AAAA,EACF;AAEA,YAAU,eAAe,KAAK,KAAK,cAAc,KAAK,GAAG,QAAQ,SAAUC,IAAG;AAC5E,WAAO,UAAUA,EAAC,IAAI,SAAUC,IAAG;AACjC,aAAO,MAAMA,EAAC,EAAE,EAAE,MAAMD,EAAC;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,QAAM,cAAc,cAAc,CAAC;AAEnC,QAAM,SAASE,OAAM;AACnB,qBAAiB,KAAK;AAEtB,oBAAgB,IAAI;AAIpB,SAAK,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACjC,aAAO,MAAM,CAAC;AACd,UAAI,KAAK;AACT,UAAI,KAAK;AAET,UAAI,SAAS,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,KAAK,UAAU;AAEhD,cAAM,OAAO,KAAK,CAAC;AAAA,MACrB,OAAO;AACL,aAAK,KAAK;AACV,kBAAU,EAAE,KAAK,KAAK,MAAM,MAAM,EAAE,SAAS,gBAAgB,IAAI,OAAO,OAAO,IAAI;AAEnF,YAAI,EAAE,aAAa,EAAE,WAAW;AAC9B,cAAI,KAAK,KAAK,GAAG;AAEf,oBAAQ,IAAI,aAAa,IAAI,OAAO,UAAU,MAAM;AAEpD,iBAAK,OAAO,GAAG,OAAO,GAAG,GAAG,KAAK;AAEjC,kBAAM,SAAS,gBAAgB,IAAI,OAAO,OAAO,IAAI;AACrD,kBAAM,MAAM,KAAK,EAAE;AACnB,iBAAK,IAAI,IAAI;AACb,qBAAS,GAAG,MAAM,UAAU,cAAc,EAAE,UAAU,EAAE;AACxD,uBAAW,eAAe,KAAK,EAAE;AAAA,UACnC,WAAW,KAAK,KAAK,KAAK,MAAM;AAE9B,eAAG,MAAM,UAAU,cAAc,EAAE,UAAU,EAAE,UAAU;AAAA,UAC3D;AAEA,eAAK,GAAG,GAAG,OAAO,KAAK;AAAA,QACzB,WAAW,EAAE,cAAc,EAAE,WAAW;AAEtC,cAAI,CAAC,EAAE,WAAW;AAEhB,cAAE,aAAa,SAAS,KAAK,CAAC;AAC9B,kBAAM,OAAO,KAAK,CAAC;AAAA,UACrB,WAAW,CAAC,EAAE,WAAW;AAEvB,cAAE,MAAM,EAAE;AACV,oBAAQ,KAAK,CAAC;AACd,kBAAM,OAAO,KAAK,CAAC;AACnB,wBAAY,UAAU,KAAK,GAAG,GAAG,OAAO,KAAK;AAAA,UAC/C;AAAA,QACF;AAEA,YAAI,CAAC,OAAO;AACV,aAAG,MAAM,WAAW,KAAK,IAAI,EAAE,OAAO,EAAE,KAAK,IAAI;AACjD,aAAG,MAAM,YAAY,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM,IAAI;AACpD,aAAG,MAAM,WAAW,KAAK,IAAI,EAAE,OAAO,EAAE,KAAK,IAAI;AACjD,aAAG,MAAM,YAAY,KAAK,IAAI,EAAE,QAAQ,EAAE,MAAM,IAAI;AAAA,QACtD;AAEA,kBAAU,eAAe,GAAG,UAAU,IAAI,WAAW;AAAA,MACvD;AAEA,kBAAY,KAAK,CAAC;AAAA,IACpB;AAEA,QAAI;AAEJ,QAAI,aAAa;AACf,qBAAe,YAAY,IAAI,SAAU,GAAG;AAC1C,eAAO,EAAE;AAAA,MACX,CAAC;AACD,gBAAU,aAAa,QAAQ,SAAU,GAAG;AAC1C,eAAO,EAAE,UAAU,OAAO,WAAW;AAAA,MACvC,CAAC;AAAA,IACH;AAEA,oBAAgB,KAAK;AAErB,QAAI,OAAO;AACT,gBAAU,SAAS,SAAUD,IAAG;AAC9B,eAAO,MAAMA,EAAC,EAAE,EAAE;AAAA,MACpB;AAEA,gBAAU,SAAS,SAAUA,IAAG;AAC9B,eAAO,MAAMA,EAAC,EAAE,EAAE;AAAA,MACpB;AAAA,IACF,OAAO;AACL,gBAAU,QAAQ,SAAUA,IAAG;AAC7B,eAAO,MAAMA,EAAC,EAAE,EAAE,QAAQ;AAAA,MAC5B;AAEA,gBAAU,SAAS,SAAUA,IAAG;AAC9B,eAAO,MAAMA,EAAC,EAAE,EAAE,SAAS;AAAA,MAC7B;AAEA,gBAAU,YAAY,KAAK,aAAa;AAAA,IAC1C;AAEA,cAAU,IAAI,SAAUA,IAAG;AACzB,aAAO,MAAMA,EAAC,EAAE,EAAE,IAAI;AAAA,IACxB;AAEA,cAAU,IAAI,SAAUA,IAAG;AACzB,aAAO,MAAMA,EAAC,EAAE,EAAE,IAAI;AAAA,IACxB;AAEA,cAAU,WAAW,SAAUA,IAAG;AAChC,aAAO,MAAMA,EAAC,EAAE,EAAE,YAAY,OAAO,SAASA,IAAG,QAAQA,EAAC,GAAG,OAAO,IAAI,MAAM;AAAA,IAChF;AAEA,cAAU,QAAQ,SAAUA,IAAG;AAC7B,aAAO,MAAMA,EAAC,EAAE,EAAE;AAAA,IACpB;AAEA,cAAU,MAAM,IAAI,SAAU,GAAG;AAC/B,aAAO,EAAE;AAAA,IACX,CAAC;AAED,QAAI,WAAW,YAAY,GAAG;AAC5B,gBAAU,YAAY;AAAA,QACpB,QAAQ,SAAS,SAAS;AACxB,iBAAO;AAAA,QACT;AAAA,MACF;AACA,gBAAU,SAAS;AACnB,gBAAU,kBAAkB,KAAK,oBAAoB;AAAA,IACvD;AAEA,aAAS,UAAU,UAAU,SAAUA,IAAG;AACxC,aAAO,MAAMA,EAAC,EAAE,KAAK,IAAI,IAAI,MAAMA,EAAC,EAAE,KAAK,IAAI,MAAMA,EAAC,EAAE,EAAE,UAAU;AAAA,IACtE;AAEA,QAAI,eAAe,QAAQ;AACzB,gBAAU,KAAK,MAAM,WAAW,OAAO;AACvC,UAAI,aAAa,QAAQ,MAAM,eAAe,MAAM;AAEpD,gBAAU,UAAU,SAAUA,IAAGE,KAAI;AACnC,eAAO,QAAQ,CAAC,eAAe,QAAQA,GAAE,IAAI,QAAQ,QAAQ,MAAMF,EAAC,EAAE,KAAK,CAAC,IAAIA,IAAGE,KAAI,UAAU;AAAA,MACnG;AAAA,IACF;AAQA,eAAW,QAAQ,SAAU,MAAM;AACjC,aAAO,KAAK,IAAI,KAAK,UAAU,cAAc,MAAM,KAAK,IAAI,GAAG,KAAK,OAAO,QAAQ,CAAC;AAAA,IACtF,CAAC;AAGD,QAAI,UAAU,QAAQ,QAAQ;AAE5B,uBAAiB,MAAM,WAAW,SAAS;AAE3C,UAAI,WAAW,QAAQ;AACrB,eAAO,SAAS,OAAO,SAAS,OAAO;AACvC,eAAO,OAAO;AAAA,MAChB;AAEA,WAAK,KAAK,QAAQ;AAChB,YAAI,MAAM,OAAO,CAAC,GAAG,YAAY;AACjC,UAAE,CAAC,IAAI,UAAU,CAAC;AAClB,UAAE,cAAc,MAAM,cAAc,cAAc,EAAE,WAAW,UAAU;AACzE,UAAE,UAAU,UAAU;AACtB,gBAAQ,KAAK,WAAW,SAAS,GAAG,CAAC;AACrC,eAAO,eAAe,CAAC;AAAA,MACzB;AAAA,IACF;AAEA,QAAI,QAAQ,UAAU,QAAQ,UAAU,SAAS,QAAQ;AACvD,qBAAe,UAAU,IAAI,WAAY;AACvC,eAAO,aAAa,cAAc,aAAa,UAAU,SAAS,IAAI,WAAW,KAAK;AAAA,MACxF,GAAG,CAAC,KAAK,CAAC,UAAU,aAAa,cAAc,aAAa,KAAK;AACjE,cAAQ,UAAU,QAAQ,KAAK,WAAW,SAAS,gBAAgB,CAAC;AAAA,IACtE;AAEA,oBAAgB,SAAS,UAAU,SAAS;AAE5C,oBAAgB,SAAS,SAAS,SAAS;AAE3C,QAAI,UAAU,UAAU,OAAO;AAE/B,QAAI,SAAS;AACX,cAAQ,IAAI,WAAW,CAAC;AAExB,aAAO,OAAO,KAAK,WAAY;AAC7B,eAAO,gBAAgB,OAAO,CAAC,UAAU;AAAA,MAC3C,CAAC;AAAA,IACH;AAEA,cAAU,UAAU,SAAS;AAC7B,cAAU,KAAK,WAAY;AACzB,UAAI,UAAU,UAAU,KAAK,KAAK;AAClC,iBAAW,CAAC,WAAW,gBAAgB,OAAO,CAAC,UAAU;AACzD,qBAAe,aAAa,cAAc,aAAa,UAAU,WAAW,KAAK;AAAA,IACnF,CAAC;AAAA,EACH;AAEA,sBAAoB,WAAW,MAAM,OAAO,SAAUC,OAAM;AAC1D,WAAO,CAACA,MAAK,MAAM,CAACA,MAAK,EAAE,aAAaA,MAAK,EAAE;AAAA,EACjD,CAAC,EAAE,IAAI,SAAUA,OAAM;AACrB,WAAOA,MAAK,EAAE;AAAA,EAChB,CAAC;AAED,MAAI,QAAQ;AACV,QAAI;AAEJ,iBAAa,cAAc,OAAO,MAAM,KAAK,MAAM,aAAa,aAAa,OAAO,QAAQ,CAAC;AAE7F,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB,OAAO;AACL,gBAAY,mBAAmB,aAAa,OAAO,QAAQ,CAAC;AAE5D,QAAI;AAAA,EACN;AAEA,MAAI,OAAO,SAAS,OAAO,WAAW;AAEtC,OAAK,SAAS,WAAY;AACxB,WAAO,UAAU,MAAM,GAAG,CAAC;AAAA,EAC7B;AAGA,SAAO;AACT;AA9zBA,IA+zBI,aAAa,SAASC,YAAW,IAAI;AACvC,KAAG,KAAK,eAAe,GAAG,KAAK,YAAY,MAAM,IAAI,GAAG,KAAK,qBAAqB,CAAC,CAAC;AACpF,KAAG,YAAY,MAAM,OAAO,IAAI,EAAE,QAAQA,WAAU;AACtD;AAl0BA,IAm0BI,YAAY,SAASC,WAAU,IAAI,QAAQ,OAAO;AAEpD,MAAI,MAAM,GAAG,SAAS,IAAI,MAAM,CAAC,GAAG,OAAO,KAAK,QAAQ;AACtD,QAAI,QAAQ;AACV,iBAAW,EAAE;AAEb,eAAS,KAAK,GAAG,SAAS,CAAC;AAE3B,SAAG,KAAK;AAAA,IACV;AAEA,WAAO;AAAA,EACT;AACF;AAh1BA,IAi1BI,gBAAgB,SAASC,eAAc,OAAO;AAChD,MAAI,SAAS,MAAM,WAAW,CAAC,GAC3B,MAAM,MAAM,MAAM,CAAC,GACnB,WAAW,MAAM,eACjB,IAAI,SAAS,QACb;AAEJ,SAAO,KAAK;AACV,cAAU,SAAS,CAAC;AACpB,WAAO,QAAQ,EAAE,IAAI,IAAI,QAAQ,EAAE,IAAI,UAAU,OAAO,QAAQ,EAAE,IAAI;AAAA,EACxE;AACF;AAEA,IAAI,YAAyB,WAAY;AACvC,WAASC,WAAU,SAAS,MAAM,yBAAyB;AACzD,SAAK,QAAQ,QAAQ,KAAK;AAC1B,SAAK,SAAS,CAAC,EAAE,QAAQ,KAAK;AAE9B,QAAI,yBAAyB;AAC3B,WAAK,UAAU,2BAA2B,OAAO;AACjD,WAAK,gBAAgB;AAErB,oBAAc,IAAI;AAAA,IACpB,OAAO;AACL,WAAK,UAAU,SAAS,OAAO;AAC/B,UAAI,OAAO,SAAS,KAAK,SAAS,SAAS,KAAK,SAAS,CAAC,KAAK;AAC/D,gBAAU,CAAC,QAAQ,OAAO,MAAM,KAAK,IAAI;AACzC,WAAK,OAAO,QAAQ,CAAC,CAAC,MAAM;AAAA,IAC9B;AAAA,EACF;AAEA,MAAI,SAASA,WAAU;AAEvB,SAAO,SAAS,SAAS,OAAO,MAAM;AACpC,QAAI,QAAQ;AAEZ,SAAK,gBAAgB,KAAK,QAAQ,IAAI,SAAU,IAAI;AAClD,aAAO,IAAI,aAAa,IAAI,MAAM,OAAO,MAAM,MAAM;AAAA,IACvD,CAAC;AAED,kBAAc,IAAI;AAElB,SAAK,UAAU,IAAI;AACnB,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAEA,SAAO,QAAQ,SAAS,QAAQ;AAC9B,SAAK,QAAQ,SAAS,KAAK,cAAc,SAAS;AAElD,kBAAc,IAAI;AAElB,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,SAAS,IAAI,OAAO,OAAO,QAAQ;AAC9C,QAAI,kBAAkB,iBAAiB,KAAK,cAAc,MAAM,CAAC,GAAG,OAAO,IAAI,GAC3E,cAAc,SAAS,MAAM,UAC7B,IAAI,GACJ,UACA;AAEJ,WAAO,IAAI,gBAAgB,QAAQ,KAAK;AACtC,iBAAW,gBAAgB,CAAC;AAC5B,iBAAW,SAAS,SAAS,gBAAgB,SAAS,SAAS,OAAO,OAAO,IAAI;AAEjF,eAAS,WAAW,SAAS,EAAE;AAC/B,gBAAU,KAAK,UAAU,QAAQ,OAAO,MAAM,GAAG,QAAQ;AACzD,eAAS,SAAS,gBAAgB,SAAS,SAAS,OAAO,OAAO,IAAI;AAAA,IACxE;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,cAAc,SAAS,YAAY,SAAS,UAAU;AAC3D,QAAI,KAAK,KAAK,gBAAgB,OAAO,KAAK;AAE1C,YAAQ,YAAY,KAAK,KAAK,GAAG,SAAS,WAAW,QAAQ;AAAA,EAC/D;AAEA,SAAO,MAAM,SAAS,IAAI,OAAO;AAC/B,QAAI,IAAI,MAAM,QAAQ,QAClB,SAAS,KAAK,UACd,MAAM,KAAK,KACX,OACA,IACA;AAEJ,WAAO,KAAK;AACV,WAAK,MAAM,cAAc,CAAC;AAC1B,YAAM,OAAO,GAAG,EAAE;AAElB,UAAI,QAAQ,GAAG,YAAY,IAAI,WAAW,IAAI,GAAG,EAAE,KAAK,IAAI,GAAG,EAAE,EAAE,YAAY,GAAG,UAAU;AAE1F,gBAAQ,KAAK,cAAc,QAAQ,GAAG,YAAY,IAAI,UAAU,MAAM,IAAI,GAAG,EAAE,CAAC;AAChF,aAAK,QAAQ,OAAO,OAAO,GAAG,MAAM,QAAQ,CAAC,CAAC;AAC9C,aAAK,cAAc,OAAO,OAAO,GAAG,EAAE;AAAA,MACxC,OAAO;AACL,aAAK,QAAQ,KAAK,MAAM,QAAQ,CAAC,CAAC;AAClC,aAAK,cAAc,KAAK,EAAE;AAAA,MAC5B;AAAA,IACF;AAEA,UAAM,gBAAgB,KAAK,cAAc;AACzC,UAAM,WAAW,KAAK,SAAS;AAE/B,kBAAc,IAAI;AAElB,WAAO;AAAA,EACT;AAEA,SAAO,UAAU,SAAS,QAAQ,OAAO;AACvC,QAAI,KAAK,MAAM,UACX,KAAK,KAAK,UACV,YAAY,CAAC,GACb,UAAU,CAAC,GACX,QAAQ,CAAC,GACT,QAAQ,CAAC,GACT,UAAU,CAAC,GACX,KAAK,MAAM,KACX,KAAK,KAAK,KACV,QAAQ,SAASC,OAAMC,KAAIC,KAAIR,KAAI;AACrC,cAAQO,IAAG,cAAcC,IAAG,YAAYD,IAAG,YAAY,QAAQ,QAAQA,IAAG,YAAY,UAAU,WAAW,KAAKP,GAAE,KAAK,QAAQ,KAAKA,GAAE;AAAA,IACxI,GACI,sBAAsB,SAASS,qBAAoBF,KAAIC,KAAIR,KAAI;AACjE,aAAO,QAAQ,QAAQA,GAAE,IAAI,KAAK,MAAMO,KAAIC,KAAIR,GAAE;AAAA,IACpD,GACI,IACA,IACA,GACA,IACA,OACA,OACA,IACA;AAEJ,SAAK,KAAK,IAAI;AACZ,cAAQ,GAAG,CAAC;AACZ,cAAQ,GAAG,CAAC;AACZ,WAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,oBAAoB,OAAO,MAAM,CAAC;AACxD,WAAK,GAAG;AACR,WAAK,GAAG,CAAC;AAET,UAAI,OAAO;AACT,aAAK,GAAG,aAAa,CAAC,MAAM,aAAa,OAAO,GAAG,UAAU,KAAK;AAClE,aAAK,SAAS,CAAC,GAAG,aAAa,CAAC,MAAM,aAAa,GAAG,YAAY,MAAM,UAAU,QAAQ;AAE1F,YAAI,GAAG,aAAa,GAAG,aAAa,GAAG,YAAY,GAAG,SAAS;AAE7D,WAAC,GAAG,YAAY,EAAE,IAAI,UAAU,WAAW,KAAK,GAAG,SAAS,GAAG,OAAO;AACtE,kBAAQ,KAAK,GAAG,SAAS,GAAG,OAAO;AAAA,QACrC,OAAO;AACL,gBAAM,IAAI,IAAI,GAAG,OAAO;AAAA,QAC1B;AAEA,iBAAS,GAAG,YAAY,MAAM,YAAY,QAAQ,GAAG,CAAC;AACtD,4BAAoB,GAAG,YAAY,GAAG,WAAW,QAAQ,QAAQ,IAAI,IAAI,GAAG,OAAO;AACnF,4BAAoB,SAAS,MAAM,YAAY,MAAM,UAAU,QAAQ,IAAI,OAAO,MAAM,OAAO;AAC/F,iBAAS,oBAAoB,OAAO,MAAM,YAAY,MAAM,UAAU,QAAQ,IAAI,MAAM,OAAO;AAAA,MACjG,OAAO;AACL,SAAC,KAAK,MAAM,KAAK,EAAE,IAAI,CAAC,GAAG,YAAY,EAAE,IAAI,UAAU,KAAK,EAAE,IAAI,MAAM,IAAI,IAAI,EAAE;AAClF,iBAAS,oBAAoB,OAAO,IAAI,MAAM,OAAO;AAAA,MACvD;AAAA,IACF;AAEA,SAAK,KAAK,IAAI;AACZ,UAAI,CAAC,GAAG,CAAC,GAAG;AACV,cAAM,KAAK,GAAG,CAAC,EAAE,OAAO;AACxB,WAAG,CAAC,KAAK,MAAM,KAAK,GAAG,CAAC,EAAE,OAAO;AAAA,MACnC;AAAA,IACF;AAEA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,SAAO,qBAAqB,SAAS,qBAAqB;AACxD,QAAI,QAAQ,qBAAqB,KAAK,KAAK,KAAK,cAC5C,IAAI,KAAK,cAAc;AAE3B,WAAO,KAAK;AACV,0BAAoB,KAAK,cAAc,CAAC,GAAG,KAAK;AAAA,IAClD;AAAA,EACF;AAEA,SAAO,YAAY,SAAS,UAAU,MAAM;AAC1C,QAAI,SAAS;AAGb,QAAI,YAAY,CAAC;AACjB,SAAK,QAAQ,QAAQ,SAAU,GAAG;AAChC,UAAI,KAAK,EAAE,OACP,kBAAkB,UAAU,IAAI,OAAO,IAAI,CAAC;AAEhD,cAAQ,mBAAmB,UAAU,QAAQ,EAAE,IAAI,KAAK,GAAG,IAAI,WAAY;AACzE,eAAO,OAAO,iBAAiB;AAAA,MACjC,CAAC;AACD,yBAAmB,UAAU,KAAK,EAAE;AAAA,IACtC,CAAC;AACD,KAAC,QAAQ,UAAU,UAAU,KAAK,iBAAiB;AAEnD,SAAK,gBAAgB,KAAK,cAAc,CAAC,CAAC,UAAU;AAAA,EACtD;AAEA,SAAO,mBAAmB,SAAS,mBAAmB;AACpD,SAAK,cAAc,QAAQ,SAAU,IAAI;AACvC,UAAI,IAAI,GAAG,QAAQ,sBAAsB;AACzC,SAAG,YAAY,CAAC,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE;AACpD,SAAG,UAAU;AAAA,IACf,CAAC;AAAA,EACH;AAEA,SAAO,kBAAkB,SAAS,gBAAgB,SAAS;AACzD,WAAO,KAAK,cAAc,KAAK,QAAQ,QAAQ,OAAO,OAAO,CAAC,CAAC;AAAA,EACjE;AAEA,SAAO,eAAe,SAAS,eAAe;AAC5C,WAAO,iBAAiB,KAAK,cAAc,MAAM,CAAC,GAAG,MAAM,IAAI,EAAE,IAAI,aAAa;AAAA,EACpF;AAEA,SAAOK;AACT,EAAE;AAEF,IAAI,eAA4B,WAAY;AAC1C,WAASK,cAAa,SAAS,OAAO,QAAQ;AAC5C,SAAK,UAAU;AACf,SAAK,OAAO,OAAO,MAAM;AAAA,EAC3B;AAEA,MAAI,UAAUA,cAAa;AAE3B,UAAQ,cAAc,SAAS,YAAY,OAAO;AAChD,QAAI,KAAK,KAAK,QACV,KAAK,MAAM;AACf,WAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,SAAS,GAAG,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,WAAW,GAAG,UAAU,CAAC,KAAK,OAAO,OAAO,MAAM,MAAM,KAAK,KAAK,YAAY,MAAM,WAAW,KAAK,SAAS,MAAM,SAAS,KAAK,UAAU,KAAK,KAAK,MAAM,KAAK,UAAU,MAAM,KAAK;AAAA,EACtQ;AAEA,UAAQ,SAAS,SAAS,OAAO,OAAO,QAAQ;AAC9C,QAAI,OAAO,MACP,UAAU,KAAK,SACf,UAAU,KAAK,YAAY,OAAO,GAClC,QAAQ,KAAK,KAAK,SAAS,OAAO,GAClC,SAAS,QAAQ,sBAAsB,GACvC,OAAO,QAAQ,WAAW,OAAO,QAAQ,YAAY,cAAc,QAAQ,SAAS,YAAY,MAAM,SAAS,QAAQ,QAAQ,GAC/H,IAAI,SAAS,IAAI,SAAS,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,kBAAkB,GAAG,OAAO,MAAM,iBAAiB,CAAC,IAAI,gBAAgB,SAAS,OAAO,OAAO,IAAI;AAC3J,UAAM,UAAU;AAEhB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,KAAK,OAAO,OAAO;AACxB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,YAAY,CAAC,EAAE,OAAO,SAAS,OAAO,UAAU,OAAO,QAAQ,OAAO;AAC3E,SAAK,UAAU,QAAQ,SAAS;AAChC,SAAK,WAAW,QAAQ,UAAU;AAClC,SAAK,SAAS,QAAQ;AACtB,SAAK,IAAI,QAAQ,GAAG;AACpB,SAAK,IAAI,QAAQ,GAAG;AACpB,SAAK,SAAS,MAAM;AACpB,SAAK,SAAS,MAAM;AACpB,SAAK,WAAW,QAAQ,UAAU;AAClC,SAAK,QAAQ,QAAQ,OAAO;AAC5B,SAAK,UAAU,QAAQ,SAAS;AAChC,SAAK,QAAQ,OAAO,KAAK,QAAQ,cAAc,QAAQ,SAAS,IAAI,GAAG,IAAI;AAE3E,SAAK,SAAS,OAAO,KAAK,SAAS,cAAc,QAAQ,UAAU,IAAI,GAAG,IAAI;AAC9E,aAAS,aAAa,MAAM,eAAe,KAAK,KAAK,cAAc,KAAK,CAAC;AACzE,SAAK,MAAM,QAAQ,UAAU,QAAQ,SAAS,YAAY,MAAM,SAAS,QAAQ,OAAO,EAAE,QAAQ;AAClG,SAAK,SAAS,UAAU,OAAO,EAAE,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,KAAK,OAAO,EAAE,CAAC,MAAM;AAE7F,SAAK,UAAU;AAAA,EACjB;AAEA,SAAOA;AACT,EAAE;AAEF,IAAI,aAA0B,WAAY;AACxC,WAASC,YAAW,MAAM,OAAO;AAC/B,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,MAAM;AAAA,EACxB;AAEA,MAAI,UAAUA,YAAW;AAEzB,UAAQ,eAAe,SAAS,aAAa,IAAI;AAC/C,QAAI,IAAI,KAAK,OAAO;AAEpB,WAAO,KAAK;AACV,UAAI,KAAK,OAAO,CAAC,EAAE,SAAS,EAAE,GAAG;AAC/B,eAAO,KAAK,OAAO,CAAC;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AAEA,UAAQ,OAAO,SAAS,OAAO;AAC7B,SAAK,MAAM,OAAO,IAAI;AAAA,EACxB;AAEA,SAAOA;AACT,EAAE;AAEF,IAAI,YAAyB,WAAY;AACvC,WAASC,WAAU,IAAI;AACrB,SAAK,KAAK;AACV,SAAK,UAAU,CAAC;AAChB,SAAK,QAAQ,CAAC;AACd,SAAK,SAAS,CAAC;AACf,SAAK,OAAO,CAAC;AACb,SAAK,OAAO,CAAC;AACb,SAAK,OAAO,CAAC;AACb,SAAK,QAAQ,IAAI,UAAU;AAC3B,SAAK,WAAW,KAAK,SAAS;AAAA,EAChC;AAEA,MAAI,UAAUA,WAAU;AAExB,UAAQ,MAAM,SAAS,IAAI,QAAQ;AACjC,QAAI,SAAS,KAAK,QAAQ,OAAO,SAAU,QAAQ;AACjD,aAAO,OAAO,SAAS;AAAA,IACzB,CAAC;AAED,QAAI,OAAO,QAAQ;AACjB,aAAO,OAAO,CAAC;AAAA,IACjB;AAEA,aAAS,IAAI,WAAW,OAAO,WAAW,aAAa;AAAA,MACrD,SAAS;AAAA,IACX,IAAI,QAAQ,IAAI;AAChB,SAAK,QAAQ,KAAK,MAAM;AACxB,WAAO;AAAA,EACT;AAEA,UAAQ,SAAS,SAAS,OAAO,QAAQ;AACvC,QAAI,IAAI,KAAK,QAAQ,QAAQ,MAAM;AACnC,SAAK,KAAK,KAAK,QAAQ,OAAO,GAAG,CAAC;AAClC,WAAO;AAAA,EACT;AAEA,UAAQ,WAAW,SAAS,SAAS,OAAO;AAC1C,QAAI,SAAS;AAEb,QAAI,YAAY,QACZ,aAAa;AACjB,aAAS;AACT,SAAK,MAAM,MAAM;AACjB,SAAK,MAAM,SAAS;AACpB,SAAK,QAAQ,QAAQ,SAAU,QAAQ;AACrC,UAAI,OAAO,KAAK,UAAU;AACxB,eAAO,OAAO,SAAS;AACvB,uBAAe;AACf,eAAO,QAAQ,OAAO,KAAK,SAAS,MAAM;AAAA,MAC5C;AAEA,eAAS,OAAO,OAAO,QAAQ,SAAU,GAAG;AAC1C,eAAO,OAAO,MAAM,IAAI,CAAC;AAAA,MAC3B,CAAC;AAAA,IACH,CAAC;AACD,mBAAe;AACf,aAAS;AACT,SAAK,cAAc;AACnB,WAAO;AAAA,EACT;AAEA,UAAQ,UAAU,SAAS,UAAU;AACnC,QAAI,SAAS;AAEb,QAAI,YAAY,QACZ,KAAK,KAAK,UACV,IAAI,KAAK,QAAQ,QACjB,aACA;AACJ,aAAS;AACT,OAAG,MAAM;AACT,SAAK,KAAK,SAAS,KAAK,OAAO,SAAS,KAAK,KAAK,SAAS;AAC3D,SAAK,QAAQ,QAAQ,SAAU,GAAG;AAChC,QAAE,KAAK,WAAW,EAAE,KAAK,QAAQ,CAAC;AAClC,UAAI,UAAU,EAAE,KAAK,SACjB,UAAU,EAAE,KAAK,SACjB,UAAU,EAAE,SACZ,GACA;AAEJ,UAAI,WAAW,QAAQ,WAAW,WAAW,UAAU;AACrD,YAAI,IAAI,UAAU;AAClB,UAAE,OAAO,QAAQ,SAAU,OAAO;AAChC,iBAAO,EAAE,IAAI,KAAK;AAAA,QACpB,CAAC;AACD,iBAAS,EAAE,QAAQ,KAAK,SAAS,OAAO,CAAC;AACzC,eAAO,MAAM,UAAU,WAAW,QAAQ,OAAO,KAAK;AACtD,eAAO,MAAM,UAAU,WAAW,QAAQ,OAAO,KAAK;AAAA,MACxD;AAAA,IACF,CAAC;AAED,uBAAmB,KAAK,IAAI;AAE5B,SAAK,KAAK,QAAQ,SAAU,GAAG;AAC7B,aAAO,EAAE;AAAA,IACX,CAAC;AAED,cAAU,GAAG,SAAS;AACtB,kBAAc,KAAK,OAAO,MAAM,CAAC;AACjC,OAAG,IAAI,WAAY;AACjB,UAAI,WAAW,GAAG,KAAK,GAAG;AAExB,oBAAY,QAAQ,SAAU,GAAG;AAC/B,iBAAO,EAAE;AAAA,QACX,CAAC;AAED,sBAAc,QAAQ,YAAY;AAAA,MACpC;AAAA,IACF,CAAC;AACD,aAAS;AAET,WAAO,KAAK;AACV,WAAK,QAAQ,CAAC,EAAE,KAAK,QAAQ,KAAK,QAAQ,CAAC,EAAE,KAAK;AAAA,IACpD;AAEA,kBAAc,MAAM,SAAS;AAE7B,OAAG,QAAQ;AACX,WAAO;AAAA,EACT;AAEA,UAAQ,YAAY,SAAS,UAAU,MAAM;AAC3C,aAAS,OAAO,SAASC,QAAO;AAC9B,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,CAAC;AACb,SAAK,QAAQ,QAAQ,SAAU,GAAG;AAChC,UAAI,EAAE,KAAK,WAAW;AACpB,YAAI,GACA,IAAI,SAASC,GAAE,SAAS;AAC1B,sBAAY,EAAE,UAAU;AACxB,cAAI,MAAM,QAAQA,EAAC;AAEnB,cAAI,CAAC,GAAG;AACN,kBAAM,OAAO,GAAG,CAAC;AACjB,kBAAM,UAAU,KAAK;AAAA,UACvB;AAAA,QACF;AAEA,cAAM,KAAK,CAAC;AACZ,UAAE,KAAK,UAAU,CAAC;AAAA,MACpB;AAAA,IACF,CAAC;AACD,UAAM,UAAU,KAAK;AACrB,WAAO;AAAA,EACT;AAEA,UAAQ,WAAW,SAAS,WAAW;AACrC,SAAK,QAAQ,QAAQ,SAAU,GAAG;AAChC,aAAO,EAAE,UAAU,EAAE,KAAK,YAAY,EAAE,KAAK,SAAS,CAAC;AAAA,IACzD,CAAC;AACD,WAAO;AAAA,EACT;AAEA,UAAQ,gBAAgB,SAAS,cAAc,MAAM;AACnD,SAAK,MAAM,UAAU,IAAI;AAEzB,SAAK,MAAM,QAAQ,SAAU,OAAO;AAClC,aAAO,MAAM,UAAU,IAAI;AAAA,IAC7B,CAAC;AAED,WAAO;AAAA,EACT;AAEA,UAAQ,MAAM,SAAS,IAAI,cAAc,OAAO;AAC9C,QAAI,SAAS;AAEb,QAAI,SAAS,QAAQ;AACnB,sBAAgB,KAAK,SAAS,KAAK;AACnC,WAAK,UAAU,WAAY;AACzB,YAAI,CAAC,OAAO,SAAS;AACnB,iBAAO,SAAS;AAEhB,iBAAO,QAAQ;AAAA,QACjB;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAEA,UAAQ,QAAQ,SAAS,MAAM,WAAW;AACxC,SAAK,MAAM,MAAM;AACjB,kBAAc,KAAK,QAAQ,SAAS;AAAA,EACtC;AAEA,UAAQ,eAAe,SAAS,aAAa,IAAI;AAC/C,QAAI,IAAI,KAAK,QAAQ,QACjB;AAEJ,WAAO,KAAK;AACV,UAAI,KAAK,QAAQ,CAAC,EAAE,aAAa,EAAE;AAEnC,UAAI,GAAG;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,KAAK,MAAM,SAAS,EAAE,KAAK,KAAK;AAAA,EACzC;AAEA,UAAQ,OAAO,SAAS,OAAO;AAC7B,SAAK,UAAU;AACf,SAAK,MAAM;AACX,WAAO,aAAa,KAAK,EAAE;AAAA,EAC7B;AAEA,SAAOF;AACT,EAAE;AAEK,IAAI,OAAoB,WAAY;AACzC,WAASG,QAAO;AAAA,EAAC;AAEjB,EAAAA,MAAK,WAAW,SAAS,SAAS,SAAS,MAAM;AAC/C,QAAI,QAAQ,YAAY,SAAS,IAAI;AAErC,oBAAgB,aAAa,OAAO,KAAK,KAAK;AAC9C,YAAQ,KAAK,SAASA,MAAK,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,KAAK;AAC5D,WAAO;AAAA,EACT;AAEA,EAAAA,MAAK,OAAO,SAAS,KAAK,OAAO,MAAM;AACrC,WAAO,QAAQ,CAAC;AAChB,oBAAgB,SAAS,KAAK,aAAa;AAC3C,WAAO,QAAQ,OAAO,YAAY,KAAK,WAAW,MAAM,SAAS;AAAA,MAC/D,OAAO,KAAK,SAAS,MAAM;AAAA,MAC3B,QAAQ,KAAK;AAAA,MACb,MAAM,CAAC,CAAC,KAAK;AAAA,IACf,CAAC,GAAG,MAAM,EAAE;AAAA,EACd;AAEA,EAAAA,MAAK,KAAK,SAAS,GAAG,OAAO,MAAM;AACjC,WAAO,QAAQ,OAAO,YAAY,KAAK,WAAW,MAAM,SAAS;AAAA,MAC/D,OAAO,KAAK,SAAS,MAAM;AAAA,MAC3B,QAAQ,KAAK;AAAA,MACb,MAAM,CAAC,CAAC,KAAK;AAAA,IACf,CAAC,GAAG,MAAM,CAAC;AAAA,EACb;AAEA,EAAAA,MAAK,SAAS,SAAS,OAAO,WAAW,SAAS,MAAM;AACtD,WAAO,QAAQ,WAAW,SAAS,IAAI;AAAA,EACzC;AAEA,EAAAA,MAAK,MAAM,SAAS,IAAI,QAAQ,MAAM,MAAM;AAC1C,QAAI,IAAI,OAAO,MAAM,MAAM,YAAY,IAAI,CAAC,GACxC,OAAO,QAAQ,GACf,WAAW,KAAK,UAChB,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,QAAQ,KAAK,OACb,eAAe,KAAK,cACpB,aAAa,KAAK,YAClB,SAAS,KAAK,QACd,WAAW,QAAQ,KAAK,YAAY,OAAO,KAAK,QAAQ,GACxD,SAAS,mBAAmB,MAAM,OAAO,QAAQ,MAAM,GACvD,QAAQ,mBAAmB,QAAQ,GAAG,QAAQ,MAAM,GACpD,cAAc,QAAQ,qBAAqB,KAAK,IAAI,cACpD,MAAM,KAAK,QAAQ;AAEvB,aAAS,YAAY,GAAG,OAAO,KAAK;AAEpC,wBAAoB,OAAO,WAAW;AAEtC,QAAI,cAAc;AAChB,2BAAqB,MAAM,EAAE,kBAAkB;AAE/C,QAAE,aAAa,WAAY;AACzB,2BAAmB,KAAK;AAExB,sBAAc,WAAW,MAAM,MAAM,SAAS;AAAA,MAChD;AAAA,IACF;AAEA,gBAAY,cAAc,OAAO,MAAM;AACvC,QAAI,KAAK,OAAO,QAAQ,SAAS,UAAU,CAAC,EAAE,YAAY,OAAO,UAAU,EAAE,YAAY,UAAU,IAAI,CAAC;AACxG,WAAO,SAAS,YAAY,YAAY,SAAS,EAAE,SAAS,KAAK;AACjE,WAAO,CAAC,WAAW,IAAI,IAAI,WAAY;AACrC,aAAO,WAAY;AACjB,eAAO,mBAAmB,KAAK;AAAA,MACjC;AAAA,IACF,CAAC;AACD,WAAO,UAAU,IAAI,EAAE,WAAW,KAAK,GAAG,MAAM,SAAS,CAAC,IAAI;AAAA,EAChE;AAEA,EAAAA,MAAK,eAAe,SAAS,aAAa,iBAAiB,MAAM;AAC/D,YAAQ,2BAA2B,YAAY,kBAAkB,IAAI,UAAU,iBAAiB,IAAI,GAAG,aAAa;AAAA,EACtH;AAEA,EAAAA,MAAK,QAAQ,SAAS,MAAM,IAAI;AAC9B,WAAO,KAAK;AACZ,WAAO,aAAa,EAAE,MAAM,aAAa,EAAE,IAAI,IAAI,UAAU,EAAE;AAAA,EACjE;AAEA,EAAAA,MAAK,cAAc,SAAS,YAAY,SAAS,UAAU;AACzD,KAAC,mBAAmB,YAAY,QAAQ,UAAU,SAAS,OAAO,GAAG,QAAQ,SAAU,GAAG;AACxF,aAAO,KAAK,UAAU,EAAE,OAAO,aAAa,QAAQ,IAAI,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH;AAEA,EAAAA,MAAK,aAAa,SAAS,WAAW,QAAQ;AAC5C,QAAI,IAAIA,MAAK,YAAY,MAAM;AAC/B,WAAO,CAAC,CAAC,KAAK,EAAE,SAAS;AAAA,EAC3B;AAEA,EAAAA,MAAK,cAAc,SAAS,YAAY,QAAQ;AAC9C,YAAQ,OAAO,MAAM,KAAK,WAAW;AAAA,EACvC;AAEA,EAAAA,MAAK,kBAAkB,SAAS,gBAAgB,QAAQ,OAAO;AAC7D,WAAO,IAAI,aAAa,OAAO,MAAM,GAAG,KAAK;AAAA,EAC/C;AAEA,EAAAA,MAAK,qBAAqB,SAAS,mBAAmB,aAAa,WAAW,OAAO;AACnF,QAAI,IAAI,gBAAgB,WAAW,MAAM,IAAI,EAAE,SAAS,gBAAgB,WAAW,CAAC;AACpF,WAAO,QAAQ,EAAE,MAAM,KAAK,IAAI;AAAA,EAClC;AAEA,EAAAA,MAAK,WAAW,SAAS,SAAS,MAAM;AACtC,IAAAhD,SAAQ,OAAO,aAAa,eAAe,SAAS;AAEpD,QAAIA,QAAO;AACT,aAAO;AAEP,cAAQA,MAAK;AAEb,iBAAW,KAAK,MAAM;AACtB,uBAAiB,KAAK,KAAK;AAC3B,UAAI,OAAO,KAAK,MAAM,KAAK,GAAG;AAE9B,sBAAgB,SAASiD,eAAc,OAAO,KAAK;AACjD,eAAO,KAAK,WAAW,KAAK,IAAI,GAAG;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAEA,SAAOD;AACT,EAAE;AACF,KAAK,UAAU;AAWf,OAAO,WAAW,eAAe,OAAO,QAAQ,OAAO,KAAK,eAAe,IAAI;", "names": ["_setDoc", "_forceNonZeroScale", "_getDocScrollTop", "_getDocScrollLeft", "_svgOwner", "_isFixed", "_createSibling", "_consolidate", "_getCTM", "_placeSiblings", "_setMatrix", "Matrix2D", "_body", "_forEachBatch", "_listToArray", "_getEl", "_round", "_toggleClass", "_camelToDashed", "_copy", "_memoizeProps", "_getInverseGlobalMatrix", "_getD<PERSON><PERSON><PERSON>h", "_orderByDOMDepth", "_recordInlineStyles", "_applyInlineStyles", "_setFinalStates", "_makeAbsolute", "_filterComps", "_makeCompsAbsolute", "_findElStateInState", "_parseElementState", "_recordProps", "_applyProps", "_getID", "_elementsFromElementStates", "_handleCallback", "_fit", "_parseState", "_getChangingElState", "_lockBodyScroll", "_fromTo", "p", "i", "run", "el", "comp", "_interrupt", "_killFlip", "_createLookup", "FlipState", "place", "s1", "s2", "placeIfDoesNotExist", "ElementState", "FlipAction", "FlipBatch", "done", "f", "Flip", "_closestTenth"]}