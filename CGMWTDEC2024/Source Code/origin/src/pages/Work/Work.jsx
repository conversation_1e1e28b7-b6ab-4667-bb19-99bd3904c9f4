import React, { useEffect } from "react";
import "./Work.css";

import Cursor from "../../components/Cursor/Cursor";
import Transition from "../../components/Transition/Transition";
import BackButton from "../../components/BackButton/BackButton";
import { ReactLenis } from "@studio-freight/react-lenis";
import InfiniteProjects from "../../components/InfiniteProjects/InfiniteProjects";

/**
 * Work page – now acts as an infinite, vertically-scrolling showcase of all
 * sibling projects. We list the projects sequentially and keep appending the
 * array when the user reaches the bottom, giving an endless feed effect.
 */
const Work = () => {
  useEffect(() => {
    // Scroll to top on mount for consistent UX
    const scrollTimeout = setTimeout(() => {
      window.scrollTo({ top: 0, behavior: "instant" });
    }, 0);
    return () => clearTimeout(scrollTimeout);
  }, []);

  return (
    <ReactLenis root>
      <Cursor />
      <div className="work-page">
        <BackButton />
        <InfiniteProjects />
      </div>
    </ReactLenis>
  );
};

export default Transition(Work);
