.video-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: black;
  cursor: pointer;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-container:hover .video-controls {
  opacity: 1;
}

.controls-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: white;
}

.time-display {
  font-size: 14px;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.control-button {
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: white;
  transition: opacity 0.2s ease;
}

.control-button:hover {
  opacity: 0.75;
}

.icon {
  width: 20px;
  height: 20px;
}
