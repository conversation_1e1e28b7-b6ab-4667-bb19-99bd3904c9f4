.infinite-projects {
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
  padding: 4em 1em;
  display: flex;
  flex-direction: column;
  gap: 6em;
}

.infinite-project {
  display: flex;
  flex-direction: column;
  gap: 1.5em;
}

.infinite-project-img {
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.infinite-project-img img {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  transition: transform 0.6s ease;
}

.infinite-project-img:hover img {
  transform: scale(1.04);
}

.infinite-project-details h2 {
  font-size: 2rem;
  line-height: 1.25;
  margin: 0 0 0.35em 0;
}

.infinite-project-details p {
  font-size: 1rem;
  line-height: 1.55;
  color: var(--light2);
  margin: 0;
}

.infinite-sentinel {
  width: 100%;
  height: 1px;
}

@media (max-width: 900px) {
  .infinite-project-details h2 {
    font-size: 1.5rem;
  }
}
