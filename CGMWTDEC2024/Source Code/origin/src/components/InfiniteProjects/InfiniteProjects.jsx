import React, { useState, useRef, useEffect, useCallback } from "react";
import "./InfiniteProjects.css";
import { projects } from "../../pages/Home/projects";

/**
 * Renders all projects in a single-column list that loops infinitely.
 * When the user reaches the bottom sentinel we append another copy of the
 * projects array, giving the appearance of an endless feed.
 */
const InfiniteProjects = () => {
  const [items, setItems] = useState(() => [...projects]);
  const loadCountRef = useRef(1); // how many times we've appended the list
  const sentinelRef = useRef(null);

  const handleIntersection = useCallback((entries) => {
    const [entry] = entries;
    if (entry.isIntersecting) {
      // Append another batch and increment counter
      loadCountRef.current += 1;
      setItems((prev) => [...prev, ...projects]);
    }
  }, []);

  useEffect(() => {
    if (!sentinelRef.current) return;
    const observer = new IntersectionObserver(handleIntersection, {
      root: null,
      rootMargin: "0px",
      threshold: 0.1,
    });
    observer.observe(sentinelRef.current);
    return () => observer.disconnect();
  }, [handleIntersection]);

  return (
    <div className="infinite-projects">
      {items.map((project, idx) => (
        <div className="infinite-project" key={`${project.id}-${idx}`}> {/* idx makes key unique after duplication */}
          <div className="infinite-project-img">
            <img src={project.image} alt={project.title} loading="lazy" />
          </div>
          <div className="infinite-project-details">
            <h2>{project.title}</h2>
            <p>{project.description}</p>
          </div>
        </div>
      ))}
      <div ref={sentinelRef} className="infinite-sentinel" />
    </div>
  );
};

export default InfiniteProjects;
