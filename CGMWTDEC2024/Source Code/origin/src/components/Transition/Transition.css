.slide-in,
.slide-out {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100%;
  background: #000;
  transform-origin: bottom;
  z-index: 100000;
  pointer-events: none;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.slide-out {
  transform-origin: top;
}

.transition-text {
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5em;
  letter-spacing: 1px;
}

.transition-text span {
  position: relative;
  top: 1px;
  font-size: 1rem;
  letter-spacing: 0;
  font-weight: lighter;
}

.loader-bg {
  position: absolute;
  width: 100%;
  height: 100%;
  will-change: transform;
}

.loader-bg img {
  transform: scale(1.1);
}
