{"name": "origin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@studio-freight/react-lenis": "^0.0.47", "dat.gui": "^0.7.9", "framer-motion": "^11.15.0", "gsap": "^3.12.5", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-router": "^7.1.1", "react-router-dom": "^7.1.1", "split-type": "^0.3.4", "three": "^0.171.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "vite": "^6.0.1"}}