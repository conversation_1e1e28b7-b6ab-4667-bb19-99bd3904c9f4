@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  user-select: none;
}

html,
body {
  width: 100%;
  height: 100%;
  font-family: "Inter";
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

a,
p {
  text-decoration: none;
  color: #fff;
  font-size: 14px;
}

nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  padding: 2em;
  display: flex;
  justify-content: center;
  gap: 2em;
  z-index: 2;
}

nav a {
  opacity: 0.5;
}

nav a#active {
  opacity: 1;
}

.slider {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.slider-images {
  position: absolute;
  width: 100%;
  height: 100%;
}

.img {
  position: absolute;
  width: 100%;
  height: 100%;
}

.slider-counter {
  position: absolute;
  bottom: 2em;
  left: 50%;
  transform: translateX(-50%);
  height: 24px;
  display: flex;
  gap: 0.5em;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}

.slider-counter > div {
  flex: 1;
}

.slider-counter p {
  line-height: 20px;
}

.counter {
  position: relative;
  top: 0px;
  will-change: transform;
}

.slider-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 64px;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}

.slider-title-wrapper {
  position: relative;
  width: 100%;
  top: 0px;
  text-align: center;
  will-change: transform;
}

.slider-title-wrapper p {
  font-size: 50px;
  line-height: 60px;
}

.slider-indicators {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75%;
  display: flex;
  justify-content: space-between;
}

.slider-indicators p {
  position: relative;
  font-size: 40px;
  font-weight: 200;
  will-change: transform;
}

.slider-preview {
  position: absolute;
  bottom: 2em;
  right: 2em;
  width: 35%;
  height: 50px;
  display: flex;
  gap: 1em;
}

.preview {
  position: relative;
  flex: 1;
  cursor: pointer;
}

.preview::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  transition: 0.3s ease-in-out;
}

.preview.active::after {
  background-color: rgba(0, 0, 0, 0);
}

@media (max-width: 900px) {
  .slider-indicators {
    width: 90%;
  }

  .slider-preview {
    width: 90%;
    bottom: 5em;
  }

  .slider-title-wrapper p {
    font-size: 30px;
  }
}
