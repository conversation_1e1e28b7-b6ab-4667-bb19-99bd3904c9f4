# Combat Mirror Detailed Image Replacement Guide

## 🎯 Project-Specific Image Requirements

### **cg-akaru-menu-js** (Navigation Menu Project)
**Current**: `assets/hero.jpg`, `assets/video.mp4`
**Replacement Strategy**:
- **hero.jpg**: Professional boxing gym with Combat Mirror installation
  - Wide shot showing modern gym with Combat Mirror prominently featured
  - Dramatic lighting, professional atmosphere
  - Resolution: 1920x1080 minimum
- **video.mp4**: Combat Mirror training demonstration
  - 30-60 second loop of fighter using Combat Mirror
  - Professional quality, good lighting
  - Focus on form correction and training benefits

### **cg-camille-mormal-slider** (Training Programs Slider)
**Current**: `assets/img1.jpg` through `assets/img8.jpg`
**Replacement Strategy**:
- **img1.jpg**: Thunder Strike Boxing - Heavy bag training with Combat Mirror
- **img2.jpg**: Warrior's Path MMA - Grappling session with mirror feedback
- **img3.jpg**: Iron Defense System - Defensive techniques practice
- **img4.jpg**: Champion's Edge Training - Elite athlete using Combat Mirror
- **img5.jpg**: Lightning Kicks Academy - Kickboxing with mirror analysis
- **img6.jpg**: Combat Flow Dynamics - Movement training session
- **img7.jpg**: Precision Strike Force - Target practice with form analysis
- **img8.jpg**: Combat Mirror Equipment - Product showcase/installation

### **cg-infinite-horizontal-slider** (Horizontal Slider)
**Current**: `public/slider_img_01.jpg` through `public/slider_img_08.jpg`
**Replacement Strategy**:
- **slider_img_01.jpg**: Professional boxing gym overview
- **slider_img_02.jpg**: MMA training facility with cage
- **slider_img_03.jpg**: Traditional martial arts dojo
- **slider_img_04.jpg**: Modern fitness center with Combat Mirror
- **slider_img_05.jpg**: Fighter development program in action
- **slider_img_06.jpg**: Group training session
- **slider_img_07.jpg**: Personal training with Combat Mirror
- **slider_img_08.jpg**: Combat Mirror technical specifications

### **cg-zajno-lp** (Landing Page)
**Current**: `assets/hero-img.jpg`, `assets/hero-img-2.jpg`, `assets/hero-img-3.jpg`
**Replacement Strategy**:
- **hero-img.jpg**: Main Combat Mirror hero shot - product in professional gym
- **hero-img-2.jpg**: Training facility overview with multiple Combat Mirrors
- **hero-img-3.jpg**: Fighter in action using Combat Mirror for form correction

### **codegrid-eseagency-scroll-carousel-javascript** (Product Carousel)
**Current**: `public/slide-img-1.jpg` through `public/slide-img-5.jpg`
**Replacement Strategy**:
- **slide-img-1.jpg**: Combat Mirror main product shot
- **slide-img-2.jpg**: Combat Mirror installation process
- **slide-img-3.jpg**: Combat Mirror in professional gym setting
- **slide-img-4.jpg**: Combat Mirror technical components
- **slide-img-5.jpg**: Combat Mirror user testimonial/action shot

## 📸 Content Sourcing Recommendations

### **Professional Photography**
1. **Gym Partnerships**: Partner with professional gyms for photo shoots
2. **Fighter Collaborations**: Work with professional fighters for action shots
3. **Equipment Photography**: Professional product photography of Combat Mirror
4. **Installation Documentation**: Document real installations in gyms

### **Stock Photography Guidelines**
When using stock photos, search for:
- "Professional boxing gym"
- "MMA training facility"
- "Martial arts dojo modern"
- "Fighter training session"
- "Boxing equipment professional"
- "Gym mirror installation"
- "Combat sports training"

### **Image Specifications**
- **Format**: JPG for photos, PNG for graphics
- **Resolution**: Minimum 1920x1080 for hero images, 1200x800 for sliders
- **Quality**: High resolution, professional lighting
- **Compression**: Optimized for web (under 500KB per image)

## 🎨 Visual Style Consistency

### **Color Grading**
- **Primary Tones**: Deep blacks, metallic silvers
- **Accent Colors**: Bold reds (boxing), deep blues (professional)
- **Lighting**: High contrast, dramatic shadows
- **Saturation**: Slightly enhanced for impact

### **Composition Guidelines**
- **Hero Images**: Wide shots showing environment and Combat Mirror
- **Action Shots**: Dynamic angles capturing movement and technique
- **Product Shots**: Clean, centered composition with good lighting
- **Facility Shots**: Architectural perspective showing professional setup

## 🔧 Technical Implementation

### **File Naming Convention**
Maintain existing file names but ensure content matches:
- `hero-img.jpg` → Combat Mirror hero content
- `img1.jpg` → Training program 1 content
- `slider_img_01.jpg` → Slider content 1
- `slide-img-1.jpg` → Carousel content 1

### **Optimization Requirements**
- **Web Performance**: Compress images for fast loading
- **Responsive Design**: Ensure images work across all device sizes
- **Accessibility**: Include descriptive alt text for all images
- **SEO**: Use Combat Mirror-relevant file names when possible

## 📋 Implementation Checklist

### **Phase 1: Critical Images** ✅
- [ ] cg-akaru-menu-js/assets/hero.jpg
- [ ] cg-zajno-lp/assets/hero-img*.jpg
- [ ] cg-p10-landing-page-reveal-gsap/public/hero-img.jpg

### **Phase 2: Main Sliders** 🔄
- [ ] cg-camille-mormal-slider/assets/img1-8.jpg
- [ ] cg-infinite-horizontal-slider/public/slider_img_01-08.jpg

### **Phase 3: Product Showcases** ⏳
- [ ] codegrid-eseagency-scroll-carousel-javascript/public/slide-img-1-5.jpg

### **Phase 4: Supporting Content** ⏳
- [ ] Gallery images across various projects
- [ ] Background images and textures
- [ ] Icon and graphic elements

## 🎯 Success Metrics

### **Brand Alignment**
- All images support Combat Mirror's professional training brand
- Visual consistency across all projects
- Authentic representation of combat sports training

### **Technical Performance**
- Fast loading times maintained
- Responsive design preserved
- Accessibility standards met
- SEO benefits from relevant content

### **User Experience**
- Images effectively communicate Combat Mirror's value
- Professional quality maintains credibility
- Visual narrative supports training and performance story

---

**Next Steps**:
1. Source or create Combat Mirror-relevant images
2. Replace images following this guide
3. Test all projects for functionality
4. Optimize images for web performance
5. Document any project-specific requirements
