# Combat Mirror Batch Image Generation Script

## 🎯 Priority 1: Hero Images (Generate These First)

### 1. Boxing Gym Hero (cg-akaru-menu-js/assets/hero.jpg)
**Dimensions**: 1792x1024
**Save as**: `hero-boxing-gym.jpg`
**Prompt**:
```
Professional boxing gym interior, modern fitness facility, large training mirror mounted on wall for form correction, dramatic cinematic lighting, high-end boxing equipment, boxing ring in background, professional atmosphere, wide shot showing facility layout, industrial ceiling, polished floors, Combat Mirror prominently featured
```

### 2. MMA Facility Hero (cg-zajno-lp/assets/hero-img.jpg)
**Dimensions**: 1792x1024
**Save as**: `hero-mma-facility.jpg`
**Prompt**:
```
MMA training facility with octagon cage, professional Combat Mirror mounted on wall, modern gym environment, fighters training in background, professional lighting, wide shot showing facility layout, high-end fitness center, clean modern design, industrial aesthetic
```

### 3. Martial Arts Dojo (cg-zajno-lp/assets/hero-img-2.jpg)
**Dimensions**: 1792x1024
**Save as**: `hero-martial-arts-dojo.jpg`
**Prompt**:
```
Traditional martial arts dojo meets modern technology, Combat Mirror installation, wooden floors, clean minimalist design, natural lighting, professional training environment, traditional meets modern aesthetic
```

### 4. Fighter Training Action (cg-zajno-lp/assets/hero-img-3.jpg)
**Dimensions**: 1792x1024
**Save as**: `hero-fighter-action.jpg`
**Prompt**:
```
Fighter in action using Combat Mirror for form correction, dynamic training session, professional gym environment, motion blur showing movement, dramatic lighting, training in progress
```

### 5. Product Showcase (cg-p10-landing-page-reveal-gsap/public/hero-img.jpg)
**Dimensions**: 1792x1024
**Save as**: `hero-product-showcase.jpg`
**Prompt**:
```
Combat Mirror training equipment product showcase, professional product photography, large mirror with precision mounting system, technical engineering detail, clean background, high-resolution product shot, professional studio lighting
```

## 🥊 Priority 2: Training Program Sliders (8 Images)

### For cg-camille-mormal-slider/assets/

#### img1.jpg - Thunder Strike Boxing
**Dimensions**: 1024x1024
**Save as**: `training-thunder-strike.jpg`
**Prompt**:
```
Professional boxer training with heavy bag, Combat Mirror visible in background for form analysis, boxing gym environment, dynamic action shot, sweat and intensity, professional lighting, training in progress
```

#### img2.jpg - Warrior's Path MMA
**Dimensions**: 1024x1024
**Save as**: `training-warriors-path.jpg`
**Prompt**:
```
MMA fighters grappling training session, Combat Mirror mounted on wall, professional training facility, ground work and grappling, action photography, modern gym environment
```

#### img3.jpg - Iron Defense System
**Dimensions**: 1024x1024
**Save as**: `training-iron-defense.jpg`
**Prompt**:
```
Martial arts defensive techniques practice, training mirror for form correction, blocking and parrying movements, dojo environment, traditional meets modern, professional setup
```

#### img4.jpg - Champion's Edge Training
**Dimensions**: 1024x1024
**Save as**: `training-champions-edge.jpg`
**Prompt**:
```
Elite athlete using Combat Mirror for championship preparation, high-performance training, professional coaching session, high-end gym facility, intense focus and dedication
```

#### img5.jpg - Lightning Kicks Academy
**Dimensions**: 1024x1024
**Save as**: `training-lightning-kicks.jpg`
**Prompt**:
```
Kickboxing training session, high kicks practice, Combat Mirror for form analysis and technique refinement, modern fitness center, dynamic movement, speed and precision
```

#### img6.jpg - Combat Flow Dynamics
**Dimensions**: 1024x1024
**Save as**: `training-combat-flow.jpg`
**Prompt**:
```
Combat flow training, movement drills, fighter using mirror for technique refinement, fluid motion, professional gym environment, dynamic training sequence
```

#### img7.jpg - Precision Strike Force
**Dimensions**: 1024x1024
**Save as**: `training-precision-strike.jpg`
**Prompt**:
```
Precision strike training, target practice with form analysis, Combat Mirror technology providing feedback, professional training setup, accuracy and technique focus
```

#### img8.jpg - Combat Mirror Equipment
**Dimensions**: 1024x1024
**Save as**: `training-equipment-showcase.jpg`
**Prompt**:
```
Combat Mirror equipment installation in professional gym, technical product showcase, mounting system detail, modern training facility, equipment focus
```

## 🚀 Quick Generation Instructions

### Using ChatGPT Plus (Recommended)
1. Copy each prompt above
2. Paste into ChatGPT with "Generate an image: [prompt]"
3. Download the generated image
4. Rename according to the "Save as" filename
5. Resize if needed to match dimensions

### Using Midjourney
1. Join Midjourney Discord
2. Use `/imagine` command with each prompt
3. Add `--ar 16:9` for hero images, `--ar 1:1` for sliders
4. Download and rename files

### Using Leonardo.ai
1. Create account at leonardo.ai
2. Use "PhotoReal" model for best results
3. Set dimensions as specified
4. Generate and download

## 📁 File Organization After Generation

Create this structure:
```
combat-mirror-images/
├── generated/
│   ├── heroes/
│   │   ├── hero-boxing-gym.jpg
│   │   ├── hero-mma-facility.jpg
│   │   ├── hero-martial-arts-dojo.jpg
│   │   ├── hero-fighter-action.jpg
│   │   └── hero-product-showcase.jpg
│   └── training/
│       ├── training-thunder-strike.jpg
│       ├── training-warriors-path.jpg
│       ├── training-iron-defense.jpg
│       ├── training-champions-edge.jpg
│       ├── training-lightning-kicks.jpg
│       ├── training-combat-flow.jpg
│       ├── training-precision-strike.jpg
│       └── training-equipment-showcase.jpg
```

## 🔄 Deployment Commands

After generating all images, run these commands to deploy them:

```bash
# Navigate to the generated images directory
cd combat-mirror-images/generated

# Deploy hero images
cp heroes/hero-boxing-gym.jpg ../../cg-akaru-menu-js/assets/hero.jpg
cp heroes/hero-mma-facility.jpg ../../cg-zajno-lp/assets/hero-img.jpg
cp heroes/hero-martial-arts-dojo.jpg ../../cg-zajno-lp/assets/hero-img-2.jpg
cp heroes/hero-fighter-action.jpg ../../cg-zajno-lp/assets/hero-img-3.jpg
cp heroes/hero-product-showcase.jpg ../../cg-p10-landing-page-reveal-gsap/public/hero-img.jpg

# Deploy training program images
cp training/training-thunder-strike.jpg ../../cg-camille-mormal-slider/assets/img1.jpg
cp training/training-warriors-path.jpg ../../cg-camille-mormal-slider/assets/img2.jpg
cp training/training-iron-defense.jpg ../../cg-camille-mormal-slider/assets/img3.jpg
cp training/training-champions-edge.jpg ../../cg-camille-mormal-slider/assets/img4.jpg
cp training/training-lightning-kicks.jpg ../../cg-camille-mormal-slider/assets/img5.jpg
cp training/training-combat-flow.jpg ../../cg-camille-mormal-slider/assets/img6.jpg
cp training/training-precision-strike.jpg ../../cg-camille-mormal-slider/assets/img7.jpg
cp training/training-equipment-showcase.jpg ../../cg-camille-mormal-slider/assets/img8.jpg
```

## ✅ Success Checklist

After deployment:
- [ ] All 13 priority images generated and deployed
- [ ] Images are properly sized and optimized
- [ ] All projects load correctly with new images
- [ ] Visual consistency maintained across projects
- [ ] Combat Mirror branding clearly visible
- [ ] Professional quality maintained

## 🎯 Estimated Time

- **Generation**: 30-45 minutes (using ChatGPT Plus or similar)
- **Organization**: 10 minutes
- **Deployment**: 5 minutes
- **Testing**: 15 minutes

**Total**: ~1 hour for complete image replacement of priority images

## 📞 Next Steps

1. **Choose your preferred AI image generator**
2. **Start with the 5 hero images** (highest impact)
3. **Generate the 8 training program images**
4. **Deploy using the provided commands**
5. **Test all projects** to ensure functionality
6. **Continue with remaining images** as needed

This approach will give you professional Combat Mirror images without relying on the problematic MCP servers! 🥊💪
