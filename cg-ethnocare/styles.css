* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html,
body {
  width: 100vw;
  height: 900vh;
  background: #000;
  color: #fff;
  font-family: "Helvetica Now Display";
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  padding: 1.5em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

p#logo {
  text-transform: uppercase;
  font-family: "PP Monument Extended";
  font-weight: 900;
}

button {
  border: none;
  outline: none;
  font-weight: 500;
  color: #000;
  padding: 0.75em 1em;
  background-color: #fff;
  border-radius: 0.25em;
}

section {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.intro {
  background: url("./assets/hero.jpg") no-repeat 50% 50%;
  background-size: cover;
}

.outro {
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(180deg, #000000, #364549);
  background-size: 200% 200%;
}

.outro p {
  width: 75%;
  text-align: center;
  color: #fff;
  font-size: 52px;
  font-weight: 400;
  line-height: 1.125;
}

.outro p span {
  color: #75e1ff;
}

.cards {
  position: absolute;
  top: 25%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 150vw;
  height: 600px;
  will-change: transform;
}

.card {
  position: absolute;
  width: 500px;
  height: 550px;
  left: 50%;
  top: 50%;
  transform-origin: center center;
  margin-left: -250px;
  display: flex;
  flex-direction: column;
  gap: 1em;
  will-change: transform;
}

.card-img {
  flex: 1;
  background-color: #fff;
  border-radius: 0.5em;
  overflow: hidden;
}

.card-content {
  width: 100%;
  height: 60px;
}

.card-content p {
  text-align: left;
  color: white;
  font-family: "Helvetica Now Display";
  font-size: 16px;
  font-weight: 500;
  line-height: 1.25;
}

.step-counter {
  position: absolute;
  display: flex;
  flex-direction: column;
  margin: 2em;
}

.counter-title,
.count {
  position: relative;
  width: 1200px;
  height: 150px;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
  overflow: hidden;
}

.count {
  position: relative;
  top: -10px;
}

.count-container {
  position: relative;
  transform: translateY(150px);
  will-change: transform;
}

.step-counter h1 {
  width: 100%;
  position: relative;
  color: #fff;
  text-transform: uppercase;
  font-family: "PP Monument Extended";
  font-weight: 900;
  font-size: 150px;
  line-height: 1;
  letter-spacing: -0.04em;
  will-change: transform;
}

.empty {
  opacity: 0;
}

.lenis.lenis-smooth {
  scroll-behavior: auto !important;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: clip;
}

.lenis.lenis-smooth iframe {
  pointer-events: none;
}

@media (max-width: 900px) {
  .counter-title {
    height: 30px;
  }

  .counter-title h1 {
    font-size: 30px;
  }

  .count {
    top: 0px;
    left: -10px;
  }

  .cards {
    top: 27.5%;
  }

  .card {
    width: 375px;
    height: 500px;
  }
}
