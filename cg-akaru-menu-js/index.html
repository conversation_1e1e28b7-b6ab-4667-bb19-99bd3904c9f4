<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Combat Mirror Navigation | Professional Training Equipment</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <div class="logo">
      <a href="#">Combat Mirror</a>
    </div>
    <div class="menu-toggle closed">
      <div class="menu-toggle-icon">
        <div class="hamburger">
          <div class="menu-bar" data-position="top"></div>
          <div class="menu-bar" data-position="bottom"></div>
        </div>
      </div>
      <div class="menu-copy">
        <p>Menu</p>
      </div>
    </div>

    <div class="menu">
      <div class="col col-1">
        <div class="menu-logo">
          <a href="#">Combat Mirror</a>
        </div>
        <div class="links">
          <div class="link"><a href="#">Training</a></div>
          <div class="link"><a href="#">Equipment</a></div>
          <div class="link"><a href="#">Technology</a></div>
          <div class="link"><a href="#">Contact</a></div>
        </div>
        <div class="video-wrapper">
          <video autoplay muted loop>
            <source src="./assets/video.mp4" type="video/mp4" />
          </video>
        </div>
      </div>
      <div class="col col-2">
        <div class="socials">
          <div class="sub-col">
            <p>Combat Mirror</p>
            <p>1247 Fighter's Way</p>
            <p>Champion City, TX 75001</p>
            <p>United States</p>
            <br />
            <p><EMAIL></p>
            <p><EMAIL></p>
          </div>
          <div class="sub-col">
            <p>Instagram</p>
            <p>LinkedIn</p>
            <p>Twitter</p>
            <p>Facebook</p>
            <br />
            <p>01 62 31 82 42</p>
          </div>
        </div>

        <div class="header">
          <h1>Avaro</h1>
        </div>
      </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/CustomEase.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        gsap.registerPlugin(CustomEase);
        CustomEase.create(
          "hop",
          "M0,0 C0.354,0 0.464,0.133 0.498,0.502 0.532,0.872 0.651,1 1,1"
        );

        const menuToggle = document.querySelector(".menu-toggle");
        const menu = document.querySelector(".menu");
        const links = document.querySelectorAll(".link");
        const socialLinks = document.querySelectorAll(".socials p");
        let isAnimating = false;

        function splitTextIntoSpans(selector) {
          let elements = document.querySelectorAll(selector);
          elements.forEach((element) => {
            let text = element.innerText;
            let splitText = text
              .split("")
              .map(function (char) {
                return `<span>${char === " " ? "&nbsp;&nbsp;" : char}</span>`;
              })
              .join("");
            element.innerHTML = splitText;
          });
        }

        splitTextIntoSpans(".header h1");

        menuToggle.addEventListener("click", () => {
          if (isAnimating) return;

          if (menuToggle.classList.contains("closed")) {
            menuToggle.classList.remove("closed");
            menuToggle.classList.add("opened");

            isAnimating = true;

            gsap.to(menu, {
              clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
              ease: "hop",
              duration: 1.5,
              onStart: () => {
                menu.style.pointerEvents = "all";
              },
              onComplete: () => {
                isAnimating = false;
              },
            });

            // Animate main links when opening
            gsap.to(links, {
              y: 0,
              opacity: 1,
              stagger: 0.1,
              delay: 0.85,
              duration: 1,
              ease: "power3.out",
            });

            // Animate social links when opening
            gsap.to(socialLinks, {
              y: 0,
              opacity: 1,
              stagger: 0.05,
              delay: 0.85,
              duration: 1,
              ease: "power3.out",
            });

            gsap.to(".video-wrapper", {
              clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
              ease: "hop",
              duration: 1.5,
              delay: 0.5,
            });

            // Animate header text rotation
            gsap.to(".header h1 span", {
              rotateY: 0,
              stagger: 0.05,
              delay: 0.75,
              duration: 1.5,
              ease: "power4.out",
            });

            // Animate header text vertical movement
            gsap.to(".header h1 span", {
              y: 0,
              scale: 1,
              stagger: 0.05,
              delay: 0.5,
              duration: 1.5,
              ease: "power4.out",
            });
          } else {
            menuToggle.classList.remove("opened");
            menuToggle.classList.add("closed");

            isAnimating = true;

            gsap.to(menu, {
              clipPath: "polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%)",
              ease: "hop",
              duration: 1.5,
              onComplete: () => {
                menu.style.pointerEvents = "none";
                gsap.set(menu, {
                  clipPath: "polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)",
                });

                gsap.set(links, { y: 30, opacity: 0 });
                gsap.set(socialLinks, { y: 30, opacity: 0 });
                gsap.set(".video-wrapper", {
                  clipPath: "polygon(0% 100%, 100% 100%, 100% 100%, 0% 100%)",
                });
                gsap.set(".header h1 span", {
                  y: 500,
                  rotateY: 90,
                  scale: 0.8,
                });

                isAnimating = false;
              },
            });
          }
        });
      });
    </script>
  </body>
</html>
