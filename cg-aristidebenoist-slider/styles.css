* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  font-family: "PP Neue Montreal";
}

.container {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

nav,
footer {
  position: fixed;
  left: 0;
  width: 100vw;
  padding: 2.75em;
  display: flex;
  justify-content: space-between;
  z-index: 2;
}

nav {
  top: 0;
}

footer {
  bottom: 0;
}

a {
  text-decoration: none;
  text-transform: uppercase;
  font-size: 12px;
  font-weight: 500;
  color: #000;
}

nav a#logo {
  position: relative;
  top: -12px;
  font-family: "Timmons NY 2.005";
  font-size: 42px;
}

.bg-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgb(213, 183, 71);
  filter: brightness(0.75);
  opacity: 0.5;
}

.slider-nav {
  position: fixed;
  top: 5%;
  left: 50%;
  transform: translateX(-50%);
  width: 25%;
  height: 15px;
  display: flex;
  justify-content: space-between;
  z-index: 10;
}

.nav-item-wrapper {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 750ms cubic-bezier(0, 0.75, 0.5, 1);
}

.nav-item {
  width: 1px;
  height: 100%;
  border: 1px solid rgba(0, 0, 0, 0.15);
  transition: all 750ms cubic-bezier(0, 0.75, 0.5, 1);
}

.nav-item-wrapper.active {
  flex: 5;
}

.nav-item-wrapper.active .nav-item {
  width: 50%;
  border: 1px solid rgba(0, 0, 0, 1);
}

.slides {
  position: fixed;
  top: 0;
  left: 0;
  width: 3000vw;
  height: 100vh;
  display: flex;
}

.slide {
  flex: 1;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slide .img {
  width: 50%;
  height: 50%;
  opacity: 0.75;
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75%;
  height: 65%;
  display: flex;
  flex-direction: column;
  pointer-events: none;
}

.slide-title-row {
  flex: 1;
  width: 100%;
  display: flex;
  gap: 0em;
}

.slide-title-row:nth-child(2) {
  position: relative;
  left: 4em;
}

.letter {
  flex: 1;
  height: 100%;
  padding-left: 2em;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}

.letter span {
  position: relative;
  display: inline-block;
  font-family: "Timmons NY 2.005";
  font-size: 280px;
  filter: brightness(0.25) saturate(0.75) !important;
}

@media (max-width: 900px) {
  .slider-nav {
    width: 40%;
  }

  .slide .img {
    width: 80%;
    height: 75%;
  }

  .slide-title {
    left: 47.5%;
    height: 25%;
  }

  .slide-title-row:nth-child(2) {
    left: 0;
  }

  .letter span {
    font-size: 100px;
  }
}
