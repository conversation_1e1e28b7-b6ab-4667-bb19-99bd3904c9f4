@import url("https://fonts.googleapis.com/css2?family=DM+Mono:ital,wght@0,300;0,400;0,500;1,300;1,400;1,500&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "DM Mono", monospace;
  background-color: #0f0f0f;
  color: #fff;
}

a,
p {
  display: block;
  color: #fff;
  text-decoration: none;
  text-transform: uppercase;
  font-size: 0.8rem;
  font-weight: 500;
}

nav,
footer {
  position: absolute;
  width: 100vw;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 2;
}

nav {
  top: 0;
}

footer {
  bottom: 0;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.slider {
  position: relative;
  width: 100vw;
  height: 100svh;
  overflow: hidden;
  user-select: none;
}

.slide-track {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
}

.slide {
  flex-shrink: 0;
  width: 350px;
  height: 500px;
  margin: 0 20px;
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  overflow: visible;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.slide-image {
  width: 100%;
  height: 100%;
  overflow: hidden;
  flex: 1;
}

.slide-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  will-change: transform;
  transform: scale(2.25);
  user-select: none;
}

.slide-overlay {
  position: absolute;
  bottom: -1.75rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  pointer-events: none;
  z-index: 10;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.slide:hover .slide-overlay {
  opacity: calc(1 - var(--slider-moving, 1));
}

.project-title {
  text-transform: uppercase;
  font-weight: 500;
  font-size: 0.8rem;
}

.project-arrow {
  width: 16px;
  height: 16px;
}

.project-arrow svg {
  stroke: #fff;
  stroke-width: 2;
}
